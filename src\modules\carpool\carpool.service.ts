import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Trip, TripDocument } from 'src/schemas/trip.schema';
import {
  BookRideDTO,
  ScheduleTripDTO,
  GetCoRideDTO,
  TargetsDTO,
  BookPrivateRideDTO,
  ConfirmPaymentDTO,
  GetCorideRequestDTO,
} from './dto/carpool.dto';
import { BaseResponseDTO } from 'src/utils/utils.types';
import { UserService } from 'src/modules/user/user.service';
import {
  EVENT,
  PAYMENT_MODE,
  REQUEST_STATUS,
  ROLE,
  TRIP_MODE,
  TRIP_STATUS,
  TRIP_TYPE,
} from 'src/utils/utils.enum';
import { MAX_DISTANCE } from 'src/utils/utils.constant';
import { AblyService } from 'src/modules/ably/ably.service';
import { TripRequest } from 'src/schemas/trip-request.schema';
import {
  formatTrip,
  formatTripRequest,
  getUserData,
} from 'src/utils/utils.formatters';
import { StripeService } from '../stripe/stripe.service';
import { Card } from 'src/schemas/card.schema';
import { PrismaService } from '../prisma/prisma.service';
import { TransactionService } from '../transaction/transaction.service';
import { TransactionStatus, TransactionType } from '@prisma/client';
import axios from 'axios';

@Injectable()
export class CarpoolService {
  readonly tripModel: Model<Trip>;
  readonly tripRequestModel: Model<TripRequest>;
  readonly cardModel: Model<Card>;

  constructor(
    @InjectModel(Trip.name) tripModel: Model<Trip>,
    @InjectModel(TripRequest.name) tripRequestModel: Model<TripRequest>,
    @InjectModel(Card.name) cardModel: Model<Card>,
    @Inject(forwardRef(() => UserService))
    private readonly userSrv: UserService,
    private readonly ablySrv: AblyService,
    private readonly stripeSrv: StripeService,
    private readonly prisma: PrismaService,
    private readonly transServ: TransactionService,
  ) {
    this.tripModel = tripModel;
    this.tripRequestModel = tripRequestModel;
    this.cardModel = cardModel;
  }

  getChannelName(trip: TripDocument) {
    return trip.mode === TRIP_MODE.carpool ? `${trip._id}` : `${trip.driver}`;
  }

  private async getDriverTripRequest(requestId: string, driverId: string) {
    const request = await this.tripRequestModel
      .findById(requestId)
      .populate('trip');
    const driver1: any = request?.driver;
    const driver2: any = request?.trip?.driver;

    if (
      !request ||
      (driver1?.toString() !== driverId && driver2.toString() !== driverId)
    ) {
      throw new BadRequestException('no trip request with that id found.');
    }
    return request;
  }

  async getTripRequests(tripId: string) {
    const requests = await this.tripRequestModel
      .find({
        trip: tripId,
      })
      .populate('user')
      .lean();
    return requests;
  }

  async getActiveTrip(driverId: string) {
    return await this.tripModel.findOne({
      driver: new Types.ObjectId(driverId),
      status: {
        $in: [TRIP_STATUS.pending, TRIP_STATUS.ongoing],
      },
      type: TRIP_TYPE.oneTime,
    });
  }

  async calculateTripCost(data: TargetsDTO) {
    const { data: res } = await axios.get(
      'https://maps.googleapis.com/maps/api/distancematrix/json?' +
        new URLSearchParams({
          origins: `${data.pickup.lng} ${data.pickup.lat}`,
          destinations: `${data.dropoff.lng} ${data.dropoff.lat}`,
          key: process.env.GOOGLE_API_KEY,
        }),
    );

    const distance = res.rows[0]?.elements?.[0]?.distance?.value;
    if (!distance) return 0;

    const basePrice = 200; //In Naira
    const pricePerKM = 50; //In Naira
    const estimatedPrice = basePrice + (pricePerKM * distance) / 1000;
    return estimatedPrice;
  }

  async getCarPoolRides(data: TargetsDTO) {
    let rides = [];
    let searchDistance = 2000; // Start with 2km

    while (rides.length === 0 && searchDistance <= MAX_DISTANCE) {
      rides = await this.tripModel
        .find({
          'origin.location': {
            $nearSphere: {
              $geometry: {
                coordinates: [data.pickup.lng, data.pickup.lat],
                type: 'Point',
              },
              $maxDistance: searchDistance,
            },
          },
          status: {
            $in: [TRIP_STATUS.pending, TRIP_STATUS.ongoing],
          },
          $expr: {
            $lt: [{ $size: '$passengers' }, '$noOfPassengers'],
          },
        })
        .populate('driver')
        .sort({
          'origin.location': 1,
        })
        .lean();

      searchDistance += 2000; // Increase the search distance by 2km
    }

    return rides.map((ride) => formatTrip(ride));
  }

  async getPrivateRides(data: TargetsDTO) {
    let results = [];
    let searchDistance = 2000; // Start with 2km

    while (results.length === 0 && searchDistance <= MAX_DISTANCE) {
      results = await this.userSrv.model
        .find({
          'lastLocation.location': {
            $nearSphere: {
              $geometry: {
                coordinates: [data.pickup.lng, data.pickup.lat],
                type: 'Point',
              },
              $maxDistance: searchDistance,
            },
          },
          privateRideActive: true,
          isOnline: true,
        })
        .sort({
          'origin.location': 1,
        })
        .lean();

      searchDistance += 2000; // Increase the search distance by 2km
    }

    const availableDrivers = results.filter(async (driver) => {
      const activeRide = await this.getActiveTrip(driver._id.toString());
      return !activeRide;
    });

    return {
      estimatedPrice: await this.calculateTripCost(data),
      drivers: availableDrivers.map((driver) => ({
        id: driver._id.toString(),
        ...getUserData(driver),
        carDetails: driver.verification?.carDetails,
      })),
    };
  }

  async scheduleTrip(
    driverId: string,
    payload: ScheduleTripDTO,
  ): Promise<BaseResponseDTO> {
    const driver = await this.userSrv.model.findById(driverId);

    if (!driver.isVerified) {
      throw new BadRequestException('Account not yet verified.');
    }

    if (driver.privateRideActive) {
      throw new BadRequestException('account currently on private trip mode.');
    }

    const existingTrip = await this.getActiveTrip(driverId);

    if (
      existingTrip &&
      existingTrip.type === TRIP_TYPE.oneTime &&
      payload.type === TRIP_TYPE.oneTime
    ) {
      throw new BadRequestException(
        `account has a ${existingTrip.status} trip. Cancel or complete the trip to schedule another trip.`,
      );
    }

    const trip = await this.tripModel.create({
      driver: driverId,
      ...payload,
      destination: {
        name: payload.destination.name,
        location: {
          coordinates: [payload.destination.lng, payload.destination.lat],
        },
      },
      origin: {
        name: payload.origin.name,
        location: {
          coordinates: [payload.origin.lng, payload.origin.lat],
        },
      },
      stops: payload.stops.map((stop) => ({
        name: stop.name,
        location: {
          coordinates: [stop.lng, stop.lat],
        },
      })),
      mode: TRIP_MODE.carpool,
    });

    return {
      status: true,
      message: 'Trip created.',
      type: 'object',
      data: formatTrip(trip),
    };
  }

  async getCoRides(payload: GetCoRideDTO): Promise<BaseResponseDTO> {
    const data =
      payload.mode === TRIP_MODE.carpool
        ? await this.getCarPoolRides(payload)
        : await this.getPrivateRides(payload);

    return {
      status: true,
      message: 'OK',
      type: 'array',
      data,
    };
  }

  async requestRide(
    userId: string,
    payload: BookRideDTO,
  ): Promise<BaseResponseDTO> {
    const trip = await this.tripModel.findById(payload.tripId);

    if (!trip) {
      throw new BadRequestException('No trip with that id found.');
    }

    /* Check if request for the current trip exists */
    const existingRequest = await this.tripRequestModel.findOne({
      user: new Types.ObjectId(userId),
      trip: trip._id,
      status: REQUEST_STATUS.pending,
    });

    if (existingRequest) {
      throw new BadRequestException('a request already exists for this trip.');
    }

    if (payload.modeOfPayment !== PAYMENT_MODE.card && payload.cardId) {
      throw new BadRequestException(
        'cardId only required when modeOfPayment is card',
      );
    }

    if (payload.modeOfPayment === PAYMENT_MODE.card) {
      if (
        !(await this.userSrv.model.findOne({
          _id: new Types.ObjectId(userId),
          cards: {
            $elemMatch: {
              $eq: new Types.ObjectId(payload.cardId),
            },
          },
        }))
      ) {
        throw new BadRequestException('invalid card id provided');
      }
    }

    const user = await this.userSrv.model.findById(userId);

    const request = await this.tripRequestModel.create({
      user: user._id,
      trip: trip._id,
      data: payload,
    });

    const data = {
      ...payload,
      driverId: trip.driver,
      requestId: request._id.toString(),
      user: {
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber,
        email: user.email,
      },
    };

    await this.ablySrv.publishEvent('drivers', EVENT.ride_request, data);

    return {
      status: true,
      message: 'ride requested successfully',
      type: 'object',
      data,
    };
  }

  // async cancelRequest(userId: string, tripId)

  async requestPrivateRide(
    userId: string,
    payload: BookPrivateRideDTO,
  ): Promise<BaseResponseDTO> {
    const driver = await this.userSrv.model.findOne({
      _id: payload.driverId,
      role: ROLE.rider,
    });

    if (!driver) {
      throw new BadRequestException('no driver with that id found.');
    }

    if (!driver.privateRideActive) {
      throw new BadRequestException(
        'driver not currently accepting private rides.',
      );
    }

    if (userId === driver._id.toString()) {
      throw new BadRequestException('you cannot request a ride from yourself.');
    }

    /* Check if request for the current trip exists */
    const existingRequest = await this.tripRequestModel.findOne({
      user: new Types.ObjectId(userId),
      driver: driver._id,
      status: REQUEST_STATUS.pending,
    });

    if (existingRequest) {
      throw new BadRequestException('requesting already sent.');
    }

    if (payload.modeOfPayment === PAYMENT_MODE.card) {
      if (
        !(await this.userSrv.model.findOne({
          _id: new Types.ObjectId(userId),
          cards: {
            $elemMatch: {
              $eq: new Types.ObjectId(payload.cardId),
            },
          },
        }))
      ) {
        throw new BadRequestException('invalid card id provided');
      }
    }

    const user = await this.userSrv.model.findById(userId);

    const request = await this.tripRequestModel.create({
      driver: driver._id,
      user: user._id,
      data: payload,
    });

    const data = {
      ...payload,
      requestId: request._id.toString(),
      user: {
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber,
        email: user.email,
      },
    };

    await this.ablySrv.publishEvent(
      'drivers',
      EVENT.private_ride_request,
      data,
    );

    return {
      status: true,
      message: 'ride requested successfully',
      type: 'object',
      data,
    };
  }

  async acceptCorideRequest(
    requestId: string,
    driverId: string,
  ): Promise<BaseResponseDTO> {
    const request = await this.getDriverTripRequest(requestId, driverId);

    if (request.status === REQUEST_STATUS.accepted) {
      throw new BadRequestException('request already accepted.');
    }
    request.status = REQUEST_STATUS.accepted;

    let data: any = {};
    if (request.driver) {
      const trip = await this.tripModel.create({
        driver: request.driver,
        passengers: [request.user],
        mode: TRIP_MODE.private,
        type: TRIP_TYPE.oneTime,
        noOfPassengers: 1,
        pricePerSeat: request.data.estimatedPrice,
        destination: {
          name: request.data.dropoff.name,
          location: {
            coordinates: [request.data.dropoff.lng, request.data.dropoff.lat],
          },
        },
        origin: {
          name: request.data.pickup.name,
          location: {
            coordinates: [request.data.pickup.lng, request.data.pickup.lat],
          },
        },
      });

      data = {
        ...formatTrip(trip),
        userId: request.user,
      };

      request.trip = trip._id as any;

      await this.ablySrv.publishEvent(
        request.driver.toString(),
        EVENT.private_ride_accepted,
        data,
      );
    } else {
      const trip = await this.tripModel
        .findById(request.trip)
        .populate('driver')
        .lean();

      data = {
        ...formatTrip(trip),
        userId: request.user,
      };
      await this.ablySrv.publishEvent(
        trip._id.toString(),
        EVENT.ride_accepted,
        data,
      );

      await this.tripModel.findByIdAndUpdate(trip._id, {
        passengers: [...trip.passengers, request.user],
      });
    }

    await request.save();
    return {
      status: true,
      message: 'request accepted.',
      type: 'object',
      data,
    };
  }

  async declineCorideRequest(
    requestId: string,
    userId: string,
  ): Promise<BaseResponseDTO> {
    const request = await this.getDriverTripRequest(requestId, userId);

    if (request.status !== REQUEST_STATUS.pending) {
      throw new BadRequestException(
        request.status === REQUEST_STATUS.declined
          ? 'request already declined.'
          : 'request not in a pending state.',
      );
    }

    request.status = REQUEST_STATUS.declined;
    await request.save();

    let data: any = {};

    if (request.driver) {
      await this.ablySrv.publishEvent(
        request.driver.toString(),
        EVENT.private_ride_declined,
        data,
      );
    } else {
      const trip = await this.tripModel.findById(request.trip);

      data = {
        tripId: trip._id,
        userId: request.user,
      };

      await this.ablySrv.publishEvent(
        trip._id.toString(),
        EVENT.ride_declined,
        data,
      );
    }

    return {
      status: true,
      message: 'request declined.',
      type: 'object',
      data,
    };
  }

  async getCorideRequests(
    driverId: string,
    payload: GetCorideRequestDTO,
  ): Promise<BaseResponseDTO> {
    const response: BaseResponseDTO = {
      status: true,
      message: 'Requests retrived.',
      type: 'array',
    };

    if (payload.mode === TRIP_MODE.carpool) {
      //TODO improve on this query
      const requests = await this.tripRequestModel
        .find({
          trip: new Types.ObjectId(payload.tripId),
        })
        .populate({
          path: 'trip',
          match: { driver: new Types.ObjectId(driverId) },
        })
        .populate('user')
        .lean();

      response.data = requests
        .filter((r) => r.trip)
        .map((request) => formatTripRequest(request));
    } else {
      const requests = await this.tripRequestModel
        .find({
          driver: new Types.ObjectId(driverId),
          status: REQUEST_STATUS.pending,
        })
        .populate('user')
        .lean();
      response.data = requests.map((request) => formatTripRequest(request));
    }
    return response;
  }

  async cancelTrip(driverId: string, tripId: string): Promise<BaseResponseDTO> {
    const trip = await this.tripModel.findOne({
      _id: new Types.ObjectId(tripId),
      driver: new Types.ObjectId(driverId),
    });

    if (!trip) {
      throw new BadRequestException('no trip with that id found.');
    }

    if (
      trip.status !== TRIP_STATUS.pending &&
      trip.status !== TRIP_STATUS.ongoing
    ) {
      throw new BadRequestException(`trip already ${trip.status}`);
    }

    trip.status = TRIP_STATUS.cancelled;
    await trip.save();

    await this.tripRequestModel.updateMany(
      {
        trip: tripId,
      },
      {
        status: REQUEST_STATUS.cancelled,
      },
    );

    await this.ablySrv.publishEvent(
      trip.mode === TRIP_MODE.carpool ? tripId : driverId,
      EVENT.ride_cancelled,
      {
        tripId,
      },
    );

    return {
      status: true,
      message: 'trip cancelled.',
    };
  }

  async rateTrip(
    passengerId: string,
    tripId: string,
    rating: number,
  ): Promise<BaseResponseDTO> {
    const request = await this.tripRequestModel
      .findOne({
        trip: new Types.ObjectId(tripId),
        user: new Types.ObjectId(passengerId),
        status: REQUEST_STATUS.accepted,
      })
      .populate('trip');

    if (!request) {
      throw new BadRequestException('trip with the provided id not found.');
    }

    if (request.rating > 0) {
      throw new BadRequestException('trip already rated.');
    }

    request.rating = rating;
    await request.save();

    return {
      status: true,
      message: 'trip rated successfully',
    };
  }

  async payForTrip(userId: string, tripId: string): Promise<BaseResponseDTO> {
    const request = await this.tripRequestModel
      .findOne({
        trip: new Types.ObjectId(tripId),
        user: new Types.ObjectId(userId),
      })
      .populate(['user', 'trip']);

    if (!request) {
      throw new BadRequestException('trip not found.');
    }

    if (request.status !== REQUEST_STATUS.accepted) {
      throw new BadRequestException('trip request was not accepted.');
    }

    if (!request.pickedup) {
      throw new BadRequestException(
        'pick up not yet confirmed for this trip request.',
      );
    }

    const channel =
      request.trip.mode === TRIP_MODE.carpool
        ? tripId
        : `${request.trip.driver}`;

    const data: any = {
      user: getUserData(request.user),
      tripData: formatTrip(request.trip),
    };

    switch (request.data.modeOfPayment) {
      case PAYMENT_MODE.cash:
        {
          data.modeOfPayment = PAYMENT_MODE.cash;
          await this.ablySrv.publishEvent(channel, EVENT.confirm_payment, data);
        }
        break;

      case PAYMENT_MODE.card:
        {
          const card = await this.cardModel.findById(request.data.cardId);
          const transExtra: any = {};
          let discount = 0;
          data.modeOfPayment = PAYMENT_MODE.card;

          if (request.user.promoCode) {
            const { id, usageCount } = request.user.promoCode;
            discount = await this.transServ.calculateDiscount(
              id,
              request.trip.pricePerSeat,
              usageCount,
            );
          }
          const isSuccessful = await this.stripeSrv.chargePaymentMethod({
            amount: request.trip.pricePerSeat - discount,
            customerId: request.user.stripeCustomerId,
            paymentMethod: card.payment_method,
          });

          if (isSuccessful) {
            await this.ablySrv.publishEvent(
              channel,
              EVENT.confirm_payment,
              data,
            );

            if (request.user.promoCode && discount > 0) {
              const { id } = request.user.promoCode;
              transExtra.promoId = id;
              transExtra.discount = discount;
              await this.prisma.promoCode.update({
                where: {
                  id,
                },
                data: {
                  totalDiscount: {
                    increment: discount,
                  },
                },
              });

              await this.userSrv.model.findByIdAndUpdate(userId, {
                $inc: {
                  'promoCode.usageCount': -1,
                },
              });
            }

            await this.prisma.transactions.create({
              data: {
                ref: this.transServ.generateTransactionRef(),
                amount: request.trip.pricePerSeat,
                type: `${data.tripData.mode.toUpperCase()}_RIDE` as TransactionType,
                userId: data.user.id,
                status: TransactionStatus.SUCCESS,
                tripId,
                driverId: `${request.trip.driver}`,
                ...transExtra,
              },
            });

            await this.tripRequestModel.findByIdAndUpdate(request._id, {
              paymentMade: true,
            });
          }
        }
        break;
    }

    return {
      status: true,
      message: 'trip paid for successfully.',
      type: 'object',
      data,
    };
  }

  async confirmPayment(
    driverId: string,
    payload: ConfirmPaymentDTO,
  ): Promise<BaseResponseDTO> {
    const trip = await this.tripModel.findOne({
      _id: new Types.ObjectId(payload.tripId),
      driver: new Types.ObjectId(driverId),
      passengers: {
        $elemMatch: {
          $eq: new Types.ObjectId(payload.requestId),
        },
      },
    });

    if (!trip) {
      throw new BadRequestException('no trip with the id or request found.');
    }

    const request = await this.tripRequestModel
      .findById(payload.requestId)
      .populate(['trip', 'user']);

    if (!request.pickedup) {
      throw new BadRequestException(
        'pick up not yet confirmed for this trip request.',
      );
    }

    if (request.paymentMade === true) {
      throw new BadRequestException('payment already confirmed.');
    }

    if (request.data.modeOfPayment === PAYMENT_MODE.cash) {
      const transExtra: any = {};
      if (request.user.promoCode) {
        const { id, usageCount } = request.user.promoCode;
        const discount = await this.transServ.calculateDiscount(
          id,
          request.trip.pricePerSeat,
          usageCount,
        );

        if (discount > 0) {
          transExtra.promoId = id;
          transExtra.discount = discount;
          await this.prisma.promoCode.update({
            where: {
              id,
            },
            data: {
              totalDiscount: {
                increment: discount,
              },
            },
          });

          await this.userSrv.model.findByIdAndUpdate(
            (request.user as any)._id,
            {
              $inc: {
                'promoCode.usageCount': -1,
              },
            },
          );
        }
      }
      await this.prisma.transactions.create({
        data: {
          ref: this.transServ.generateTransactionRef(),
          amount: trip.pricePerSeat,
          type: `${trip.mode.toUpperCase()}_RIDE` as TransactionType,
          userId: `${request.user}`,
          driverId: `${request.trip.driver}`,
          status: TransactionStatus.SUCCESS,
          tripId: payload.tripId,
          ...transExtra,
        },
      });

      request.paymentMade = true;
      await request.save();
    }

    return {
      status: true,
      message: 'payment confirmed.',
    };
  }

  async endTrip(driverId: string, tripId): Promise<BaseResponseDTO> {
    const trip = await this.tripModel.findOne({
      driver: new Types.ObjectId(driverId),
      _id: tripId,
    });

    if (!trip) {
      throw new BadRequestException('trip with the id not found.');
    }

    if (trip.status === TRIP_STATUS.completed) {
      throw new BadRequestException('trip already ended.');
    }

    if (trip.status !== TRIP_STATUS.ongoing) {
      throw new BadRequestException('only an ongoing trip can be ended.');
    }

    trip.status = TRIP_STATUS.completed;
    await trip.save();
    return {
      status: true,
      message: 'trip ended successfully.',
      type: 'object',
      data: {
        tripId: trip._id.toString(),
      },
    };
  }

  async confirmPickUp(
    driverId: string,
    requestId: string,
  ): Promise<BaseResponseDTO> {
    const request = await this.getDriverTripRequest(requestId, driverId);

    if (request.pickedup) {
      throw new BadRequestException('pick up already confirmed.');
    }

    if (request.status !== REQUEST_STATUS.accepted) {
      throw new BadRequestException('request not yet accepted.');
    }

    request.pickedup = true;
    request.pickupTime = new Date();
    await request.save();

    await this.tripModel.findByIdAndUpdate(request.trip, {
      status: TRIP_STATUS.ongoing,
    });

    const data = {
      requestId,
      userId: `${request.user}`,
    };

    await this.ablySrv.publishEvent(
      this.getChannelName(request.trip as any),
      EVENT.pickup_confirmed,
      data,
    );

    return {
      status: true,
      message: 'pick up confirmed.',
      type: 'object',
      data,
    };
  }
}

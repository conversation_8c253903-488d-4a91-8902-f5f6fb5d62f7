import { BadRequestException, Injectable } from '@nestjs/common';
import { UserService } from 'src/modules/user/user.service';
import { ROLE, VERIFICATION_STATUS } from 'src/utils/utils.enum';
import { BaseResponseDTO } from 'src/utils/utils.types';

@Injectable()
export class VerificationService {
  constructor(private readonly userSrv: UserService) {}

  async markUserAsVerified(userId: string): Promise<BaseResponseDTO> {
    const user = await this.userSrv.model.findById(userId);

    if (!user) {
      throw new BadRequestException('user not found.');
    }

    if (user.isVerified) {
      throw new BadRequestException('user already verified.');
    }

    user.isVerified = true;
    user.role = ROLE.rider;
    user.verificationStatus = VERIFICATION_STATUS.approved;
    await user.save();

    return {
      status: true,
      message: 'User verified.',
    };
  }

  async getVerificationRequests(): Promise<BaseResponseDTO> {
    const users = await this.userSrv.model.find({
      verificationStatus: VERIFICATION_STATUS.pending,
    });

    return {
      status: true,
      message: 'OK',
      type: 'array',
      data: users.map((user) => ({
        verifcationData: user.verification,
        userData: {
          id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phoneNumber: user.phoneNumber,
        },
      })),
    };
  }
}

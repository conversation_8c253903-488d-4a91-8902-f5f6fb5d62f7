import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  ChangePasswordDTO,
  GoogleAuthDTO,
  ResetPasswordDTO,
  SendOTPDTO,
  SignInDTO,
  SignUpDTO,
  VerifyOTPDTO,
  VerifyPasswordResetOTPDTO,
} from './dto/auth.dto';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { CurrentUser } from 'src/utils/decorators/custom.decorators';
import { AuthUser } from 'src/utils/utils.types';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  async signUp(@Body() payload: SignUpDTO) {
    return await this.authService.signUp(payload);
  }

  @Post('signup-google')
  async signUpWithGoogle(@Body() payload: GoogleAuthDTO) {
    return await this.authService.signUpWithGoogle(payload.accessToken);
  }

  @Post('signup-facebook')
  async signUpWithFacebook(@Body() payload: GoogleAuthDTO) {
    return await this.authService.signUpWithFacebook(payload.accessToken);
  }

  @Post('signin')
  @HttpCode(HttpStatus.OK)
  async signIn(@Body() payload: SignInDTO) {
    return await this.authService.signIn(payload);
  }

  @Post('signin-google')
  @HttpCode(HttpStatus.OK)
  async signInWithGoogle(@Body() payload: GoogleAuthDTO) {
    return await this.authService.signinWithGoogle(payload.accessToken);
  }

  @Post('signin-facebook')
  @HttpCode(HttpStatus.OK)
  async signInWithFacebook(@Body() payload: GoogleAuthDTO) {
    return await this.authService.signinWithFacebook(payload.accessToken);
  }

  @Post('send-otp')
  @HttpCode(HttpStatus.OK)
  async sendOTP(@Body() payload: SendOTPDTO) {
    return await this.authService.sendOTP(payload.phoneNumber);
  }

  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  async verifyOTP(@Body() payload: VerifyOTPDTO) {
    return await this.authService.verifyOTP(payload);
  }

  @Post('verify-password-reset-otp')
  @HttpCode(HttpStatus.OK)
  async verifyPasswordResetOTP(@Body() payload: VerifyPasswordResetOTPDTO) {
    return await this.authService.verifyPasswordResetOTP(payload);
  }

  @Get('request-password-reset/:email')
  async requestPasswordReset(@Param('email') email: string) {
    return await this.authService.requestPasswordReset(email);
  }

  @UseGuards(AuthGuard)
  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(
    @Body() payload: ResetPasswordDTO,
    @CurrentUser() user: AuthUser,
  ) {
    return await this.authService.resetPassword(user.id, payload);
  }

  @UseGuards(AuthGuard)
  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  async changePassword(
    @Body() payload: ChangePasswordDTO,
    @CurrentUser() user: AuthUser,
  ) {
    return await this.authService.changePassword(user.id, payload);
  }
}

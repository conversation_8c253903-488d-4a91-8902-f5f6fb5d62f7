import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Card } from 'src/schemas/card.schema';
import { User } from 'src/schemas/user.schema';
import { pick } from 'src/utils/utils.functions';
import {
  BaseResponseDTO,
  ChargeCardData,
  CreateStripeCustomerData,
} from 'src/utils/utils.types';
import Stripe from 'stripe';
import { AblyService } from '../ably/ably.service';
import { PrismaService } from '../prisma/prisma.service';
import { TransactionService } from '../transaction/transaction.service';

@Injectable()
export class StripeService {
  private readonly client: Stripe;
  private readonly userModel: Model<User>;
  private readonly cardModel: Model<Card>;

  constructor(
    @InjectModel(User.name) userModel: Model<User>,
    @InjectModel(Card.name) cardModel: Model<Card>,
    private readonly ablySrv: AblyService,
    private readonly prisma: PrismaService,
    private readonly transServ: TransactionService,
  ) {
    this.client = new Stripe(process.env.STRIPE_API_KEY!);
    this.userModel = userModel;
    this.cardModel = cardModel;
  }

  verifyWebhook(sig: string, payload: any) {
    try {
      return this.client.webhooks.constructEvent(
        payload,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET!,
      );
    } catch (err) {
      console.log(err);
      throw new UnauthorizedException('invalid webhook event.');
    }
  }

  async handleCardAddedEvent(
    payload: Stripe.Response<Stripe.SetupIntentSucceededEvent>,
  ) {
    const method = await this.client.paymentMethods.retrieve(
      payload.data.object.payment_method as string,
    );

    const newCard = await this.cardModel.create({
      ...pick(method.card, ['brand', 'last4']),
      payment_method: method.id,
    });

    await this.userModel.findOneAndUpdate(
      {
        stripeCustomerId: payload.data.object.customer,
      },
      {
        $push: {
          cards: newCard._id,
        },
      },
    );
  }

  async createPaymentIntent(userId: string) {
    const user = await this.userModel.findById(userId);
    let customer: string = user.stripeCustomerId;

    if (!user.stripeCustomerId) {
      const data: CreateStripeCustomerData = {
        name: `${user.firstName} ${user.lastName}`,
        email: user.email,
      };
      if (user.phoneNumber) {
        data.phone = user.phoneNumber;
      }
      customer = await this.createCustomer(data);
      await this.userModel.findByIdAndUpdate(user._id, {
        stripeCustomerId: customer,
      });
    }
    return await this.client.setupIntents.create({
      customer,
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never',
      },
    });
  }

  async createCustomer(data: CreateStripeCustomerData) {
    const res = await this.client.customers.create(data);
    return res.id;
  }

  async detachPaymentMethod(
    userId: string,
    cardId: string,
  ): Promise<BaseResponseDTO> {
    try {
      const user = await this.userModel
        .findOne({
          _id: new Types.ObjectId(userId),
          cards: {
            $elemMatch: {
              $eq: new Types.ObjectId(cardId),
            },
          },
        })
        .populate({
          path: 'cards',
          model: 'Card',
          match: {
            _id: new Types.ObjectId(cardId),
          },
        });

      if (!user) {
        throw new BadRequestException(
          'no card with the provided id found on this account.',
        );
      }

      await this.client.paymentMethods.detach(user.cards[0].payment_method);

      await this.cardModel.findByIdAndDelete(cardId);
      await this.userModel.findByIdAndUpdate(userId, {
        $pull: {
          cards: {
            $eq: new Types.ObjectId(cardId),
          },
        },
      });

      return {
        status: true,
        message: 'card detached.',
      };
    } catch {
      throw new BadRequestException('invalid card id provided.');
    }
  }

  async chargePaymentMethod(data: ChargeCardData) {
    const response = await this.client.paymentIntents.create({
      amount: data.amount * 100,
      currency: 'ngn',
      payment_method: data.paymentMethod,
      customer: data.customerId,
      confirm: true,
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: 'never',
      },
    });

    if (response.status === 'succeeded') {
      return true;
    }
    return false;
  }
}

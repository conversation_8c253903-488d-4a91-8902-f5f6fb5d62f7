import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UserService } from './user.service';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { CurrentUser } from 'src/utils/decorators/custom.decorators';
import { AuthUser } from 'src/utils/utils.types';
import {
  AddressDTO,
  ApplyPromoCodeDTO,
  GetRideDTO,
  IdentityDTO,
  VehicleDetailsDTO,
  VerificationImageDTO,
} from './dto/user.dto';
import { FormDataRequest } from 'nestjs-form-data';
import { Roles } from 'src/utils/decorators/roles.decorator';
import { ROLE } from 'src/utils/utils.enum';
import { RolesGuard } from 'src/utils/gaurds/roles.gaurd';
import { Location } from 'src/utils/utils.dto';

@Controller('user')
@UseGuards(AuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  async getProfile(@CurrentUser() user: AuthUser) {
    return await this.userService.getProfile(user.id);
  }

  @Patch('address')
  async updateAddress(
    @CurrentUser() user: AuthUser,
    @Body() payload: AddressDTO,
  ) {
    return await this.userService.saveAddresses(user.id, payload);
  }

  @Patch('location')
  async updateLocation(
    @CurrentUser() user: AuthUser,
    @Body() payload: Location,
  ) {
    return await this.userService.updateUserLocation(user.id, payload);
  }

  @Post('verification/car-details')
  async updateCarDetails(
    @CurrentUser() user: AuthUser,
    @Body() payload: VehicleDetailsDTO,
  ) {
    return await this.userService.updateVehicleDetails(user.id, payload);
  }

  @Post('verification/identity')
  async updateIdentity(
    @CurrentUser() user: AuthUser,
    @Body() payload: IdentityDTO,
  ) {
    return await this.userService.updateIdentity(user.id, payload);
  }

  @Post('verification/image')
  @FormDataRequest()
  async uploadVerificationImage(
    @CurrentUser() user: AuthUser,
    @Body() payload: VerificationImageDTO,
  ) {
    return await this.userService.uploadVerificationImage(user.id, payload);
  }

  @Get('verification/complete')
  async requestVerification(@CurrentUser() user: AuthUser) {
    return await this.userService.requestVerification(user.id);
  }

  @Get('active-trip')
  async getPendingOrOngoingTrip(@CurrentUser() user: AuthUser) {
    return await this.userService.getPendingOrOngoingTrip(user);
  }

  @Get('activate-private-ride')
  @Roles(ROLE.rider)
  @UseGuards(RolesGuard)
  async activatePrivateRide(@CurrentUser() driver: AuthUser) {
    return await this.userService.activatePrivateRide(driver.id, true);
  }

  @Delete('activate-private-ride')
  @Roles(ROLE.rider)
  @UseGuards(RolesGuard)
  async deactivatePrivateRide(@CurrentUser() driver: AuthUser) {
    return await this.userService.activatePrivateRide(driver.id, false);
  }

  @Get('rides')
  async getRides(@CurrentUser() user: AuthUser, @Query() query: GetRideDTO) {
    return await this.userService.getRides(user.id, query);
  }

  @Post('promo-code')
  @Roles(ROLE.rider, ROLE.user)
  @HttpCode(HttpStatus.OK)
  async applyPromoCode(
    @CurrentUser() user: AuthUser,
    @Body() payload: ApplyPromoCodeDTO,
  ) {
    return await this.userService.applyPromoCode(payload.code, user.id);
  }
}

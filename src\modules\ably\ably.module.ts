import { Global, Module } from '@nestjs/common';
import { AblyService } from './ably.service';
import { AblyController } from './ably.controller';
import { ConfigService } from '@nestjs/config';
import Ably from 'ably';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'src/schemas/user.schema';

const AblyProvider = {
  provide: 'ABLY_APP',
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => {
    const apiKey = configService.get<string>('ABLY_API_KEY');
    return new Ably.Rest({
      key: apiKey,
      clientId: 'server',
    });
  },
};

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
  ],
  controllers: [AblyController],
  providers: [AblyProvider, AblyService],
  exports: [AblyService],
})
export class AblyModule {}

export enum ROLE {
  user = 'user',
  rider = 'rider',
  admin = 'admin',
}

export enum VERIFICATION_STATUS {
  not_started = 'NOT_STARTED',
  pending = 'PENDING',
  inProgress = 'IN_PROGRESS',
  declined = 'DECLINED',
  approved = 'APPROVED',
}

export enum SIGNUP_MODE {
  google = 'google',
  facebook = 'facebook',
  apple = 'apple',
  basic = 'basic',
}

export enum IDENTITY_TYPE {
  bvn = 'BVN',
  nin = 'NIN',
}

export enum VERIFICATION_IMAGE_TYPE {
  motorInsurance = 'motorInsurance',
  roadWorthiness = 'roadWorthiness',
  vehicleLicense = 'vehicleLicense',
  driverLicenseFront = 'driverLicense_front',
  driverLicenseBack = 'driverLicense_back',
}

export enum STORAGE_DIRECTORY {
  verification = 'verification',
}

export enum TRIP_TYPE {
  oneTime = 'one-time',
  recurring = 'recurring',
}

export enum TRIP_MODE {
  carpool = 'carpool',
  private = 'private',
}

export enum PAYMENT_MODE {
  card = 'card',
  cash = 'cash', //also applies for google pay and apple pay.
}

export enum EVENT {
  ride_request = 'ride-request',
  ride_accepted = 'ride-accepted',
  ride_declined = 'ride-declined',

  /* Private rides */
  private_ride_request = 'private-ride-request',
  private_ride_accepted = 'private-ride-accepted',
  private_ride_declined = 'private-ride-declined',

  ride_cancelled = 'ride-cancelled',

  confirm_payment = 'confirm-paymennt',
  pickup_confirmed = 'pickup-confirmed',
}

export enum CHANNELS {
  drivers = 'presence-drivers',
  allusers = 'private-users',
}

export enum REQUEST_STATUS {
  pending = 'pending',
  accepted = 'accepted',
  declined = 'declined',
  cancelled = 'cancelled',
}

export enum TRIP_STATUS {
  pending = 'pending',
  ongoing = 'ongoing',
  cancelled = 'cancelled',
  completed = 'completed',
}

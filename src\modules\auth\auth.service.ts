import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  ServiceUnavailableException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { compareSync, genSalt, hash } from 'bcryptjs';

import {
  ChangePasswordDTO,
  ResetPasswordDTO,
  SignInDTO,
  SignUpDTO,
  VerifyOTPDTO,
  VerifyPasswordResetOTPDTO,
} from './dto/auth.dto';
import { AuthUser, BaseResponseDTO } from 'src/utils/utils.types';
import { UserService } from 'src/modules/user/user.service';
import { omit } from 'src/utils/utils.functions';
import { axiosInstance } from 'src/utils/utils.constant';
import { Token } from 'src/schemas/token.schema';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { addMinutes, differenceInMinutes } from 'date-fns';
import { PrismaService } from 'src/modules/prisma/prisma.service';
import { OAuth2Client } from 'google-auth-library';
import { StripeService } from '../stripe/stripe.service';
import { SIGNUP_MODE } from 'src/utils/utils.enum';

@Injectable()
export class AuthService {
  constructor(
    private readonly userServ: UserService,
    private readonly jwtService: JwtService,
    private readonly prisma: PrismaService,
    private readonly stripeSrv: StripeService,
    @InjectModel(Token.name) private readonly tokenModel: Model<Token>,
  ) {}

  generateAuthToken(payload: AuthUser): string {
    const token = this.jwtService.sign(payload, {
      expiresIn: '3d',
      secret: process.env.JWT_SECRET,
    });
    return token;
  }

  async getGoogleUserData(access_token: string): Promise<any> {
    try {
      const client = new OAuth2Client(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
      );

      client.setCredentials({ access_token });

      const { data } = await client.request({
        url: 'https://www.googleapis.com/oauth2/v3/userinfo', // URL for user info endpoint
        method: 'GET',
      });
      return data;
    } catch {
      throw new BadRequestException('invalid or expired token');
    }
  }

  async getFacebookUserData(accessToken: string) {
    const { data, status } = await axiosInstance.get(
      'https://graph.facebook.com/me?' +
        new URLSearchParams({
          access_token: accessToken,
          fields: 'name,email',
        }),
    );

    if (status !== HttpStatus.OK) {
      throw new BadRequestException('invalid or expired token');
    }

    return data;
  }

  async generateHash(password: string): Promise<string> {
    const salt = await genSalt(10);
    return await hash(password, salt);
  }

  comparePassword(password: string, passwordHash: string): boolean {
    return compareSync(password, passwordHash);
  }

  generateOtp() {
    const otp = Math.floor(1000 + Math.random() * 9000);
    return otp.toString();
  }

  async signUp(payload: SignUpDTO): Promise<BaseResponseDTO> {
    const existingUser = await this.userServ.model
      .findOne({
        email: payload.email,
      })
      .lean();

    if (existingUser) {
      throw new ConflictException('user with the email already exist.');
    }

    const customerId = await this.stripeSrv.createCustomer({
      name: `${payload.firstName} ${payload.lastName}`,
      phone: payload.phoneNumber,
      email: payload.email.toLowerCase(),
    });

    const user = await this.userServ.model.create({
      ...payload,
      password: await this.generateHash(payload.password),
      stripeCustomerId: customerId,
    });

    await this.prisma.wallet.create({
      data: {
        id: user._id.toString(),
      },
    });

    return {
      status: true,
      message: 'OK',
      type: 'object',
      data: omit(user.toObject(), ['password', '__v']),
    };
  }

  async signUpWithGoogle(accessToken: string): Promise<BaseResponseDTO> {
    const userInfo = await this.getGoogleUserData(accessToken);

    if (
      await this.userServ.model.findOne({
        email: userInfo.email,
      })
    ) {
      throw new ConflictException('user with the email already exist.');
    }

    const firstName = userInfo.given_name;
    const lastName = userInfo.family_name;
    const customerId = await this.stripeSrv.createCustomer({
      name: `${firstName} ${lastName}`,
      email: userInfo.email,
    });

    const user = await this.userServ.model.create({
      firstName,
      lastName,
      email: userInfo.email,
      stripeCustomerId: customerId,
      signupMode: SIGNUP_MODE.google,
    });

    await this.prisma.wallet.create({
      data: {
        id: user._id.toString(),
      },
    });

    return {
      status: true,
      message: 'OK',
      type: 'object',
      data: {
        id: user._id.toString(),
        ...omit(user.toObject(), ['password', '__v', '_id']),
      },
    };
  }

  async signUpWithFacebook(accessToken: string): Promise<BaseResponseDTO> {
    const userInfo = await this.getFacebookUserData(accessToken);

    if (
      await this.userServ.model.findOne({
        email: userInfo.email,
      })
    ) {
      throw new ConflictException('user with the email already exist.');
    }

    const [firstName, lastName] = userInfo.name.split(' ');
    const customerId = await this.stripeSrv.createCustomer({
      name: `${firstName} ${lastName}`,
      email: userInfo.email,
    });

    const user = await this.userServ.model.create({
      firstName,
      lastName,
      email: userInfo.email,
      stripeCustomerId: customerId,
      signupMode: SIGNUP_MODE.facebook,
    });

    await this.prisma.wallet.create({
      data: {
        id: user._id.toString(),
      },
    });

    return {
      status: true,
      message: 'OK',
      type: 'object',
      data: {
        id: user._id.toString(),
        ...omit(user.toObject(), ['password', '__v', '_id']),
      },
    };
  }

  async signIn(payload: SignInDTO): Promise<BaseResponseDTO> {
    const user = await this.userServ.model.findOne({
      email: payload.email,
      signupMode: SIGNUP_MODE.basic,
    });

    if (!user) {
      throw new BadRequestException('Incorrect email or password.');
    }

    if (user.isBanned) {
      throw new ForbiddenException('account banned.');
    }

    if (!this.comparePassword(payload.password, user.password)) {
      throw new BadRequestException('Incorrect email or password.');
    }

    return {
      status: true,
      message: 'Login successful.',
      type: 'object',
      data: {
        ...(await this.userServ.getProfile(user._id.toString())).data,
        token: this.generateAuthToken({
          email: user.email,
          id: user._id.toString(),
          role: user.role,
        }),
      },
    };
  }

  async signinWithGoogle(accessToken: string): Promise<BaseResponseDTO> {
    const userInfo = await this.getGoogleUserData(accessToken);

    const user = await this.userServ.model.findOne({
      email: userInfo.email,
      signupMode: SIGNUP_MODE.google,
    });

    if (!user) {
      throw new BadRequestException('account not found.');
    }

    if (user.isBanned) {
      throw new ForbiddenException('account banned.');
    }

    return {
      status: true,
      message: 'Login successful.',
      type: 'object',
      data: {
        ...(await this.userServ.getProfile(user._id.toString())).data,
        token: this.generateAuthToken({
          email: user.email,
          id: user._id.toString(),
          role: user.role,
        }),
      },
    };
  }

  async signinWithFacebook(accessToken: string): Promise<BaseResponseDTO> {
    const userInfo = await this.getFacebookUserData(accessToken);

    const user = await this.userServ.model.findOne({
      email: userInfo.email,
      signupMode: SIGNUP_MODE.facebook,
    });

    if (!user) {
      throw new BadRequestException('account not found.');
    }

    if (user.isBanned) {
      throw new ForbiddenException('account banned.');
    }

    return {
      status: true,
      message: 'Login successful.',
      type: 'object',
      data: {
        ...(await this.userServ.getProfile(user._id.toString())).data,
        token: this.generateAuthToken({
          email: user.email,
          id: user._id.toString(),
          role: user.role,
        }),
      },
    };
  }

  async sendOTP(phoneNumber: string): Promise<BaseResponseDTO> {
    const { data, status } = await axiosInstance.post(
      process.env.TERMII_BASE_URL + '/api/sms/otp/send',
      {
        api_key: process.env.TERMII_API_KEY,
        channel: 'dnd',
        from: 'N-Alert',
        message_text:
          'Your CoRide OTP is < 1234 >. This OTP expires in 10 minutes.',
        message_type: 'NUMERIC',
        pin_attempts: 5,
        pin_length: 4,
        pin_placeholder: '< 1234 >',
        pin_time_to_live: 10,
        to: phoneNumber,
        pin_type: 'NUMERIC',
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    if (status !== 200) {
      throw new ServiceUnavailableException('An error occurred.');
    }

    return {
      status: true,
      message: 'OK',
      type: 'object',
      data,
    };
  }

  async verifyOTP(payload: VerifyOTPDTO): Promise<BaseResponseDTO> {
    const { data, status } = await axiosInstance.post(
      process.env.TERMII_BASE_URL + '/api/sms/otp/verify',
      {
        api_key: process.env.TERMII_API_KEY,
        ...payload,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    if (status !== 200) {
      throw new ServiceUnavailableException('An error occurred.');
    }

    if (data.verified !== true) {
      throw new BadRequestException('Invalid or Expired token.');
    }

    return {
      status: true,
      message: 'OK',
      type: 'object',
      data,
    };
  }

  async requestPasswordReset(email: string): Promise<BaseResponseDTO> {
    const user = await this.userServ.model.findOne({
      email,
    });

    if (user) {
      const code = this.generateOtp();

      await axiosInstance.post(
        process.env.TERMII_BASE_URL + '/api/email/otp/send',
        {
          api_key: process.env.TERMII_API_KEY,
          email_address: email,
          code,
          email_configuration_id: process.env.TERMII_EMAIL_CONFIGURATION_ID,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      const existToken = await this.tokenModel.findOne({
        email,
      });

      if (existToken) {
        await this.tokenModel.updateOne(
          {
            _id: existToken._id,
          },
          {
            createdAt: new Date(),
            expiresAt: addMinutes(new Date(), 10),
            pin: await this.generateHash(code),
          },
        );
      } else {
        await this.tokenModel.create({
          pin: await this.generateHash(code),
          email,
        });
      }
    }

    return {
      status: true,
      message: 'Pin sent to email if account exists',
    };
  }

  async verifyPasswordResetOTP(
    payload: VerifyPasswordResetOTPDTO,
  ): Promise<BaseResponseDTO> {
    const token = await this.tokenModel.findOne({
      email: payload.email,
    });

    if (!token) {
      throw new BadRequestException('Invalid or expired token.');
    }

    if (
      !this.comparePassword(payload.pin, token.pin) ||
      differenceInMinutes(token.expiresAt, token.createdAt) > 10
    ) {
      throw new BadRequestException('Invalid or expired token.');
    }

    const user = await this.userServ.model
      .findOne({
        email: token.email,
      })
      .lean();

    await this.tokenModel.deleteOne({
      _id: token._id,
    });

    return {
      status: true,
      message: 'Code valid',
      type: 'object',
      data: {
        token: this.generateAuthToken({
          email: user.email,
          id: user._id.toString(),
          role: user.role,
        }),
      },
    };
  }

  async resetPassword(
    userId: string,
    payload: ResetPasswordDTO,
  ): Promise<BaseResponseDTO> {
    await this.userServ.model.updateOne(
      {
        _id: new Types.ObjectId(userId),
      },
      {
        password: await this.generateHash(payload.newPassword),
      },
    );

    return {
      status: true,
      message: 'Password reset successful.',
    };
  }

  async changePassword(
    userId: string,
    payload: ChangePasswordDTO,
  ): Promise<BaseResponseDTO> {
    const user = await this.userServ.model.findById(userId);

    if (!this.comparePassword(payload.oldPassword, user.password)) {
      throw new BadRequestException('Incorrect password entered.');
    }

    user.password = await this.generateHash(payload.newPassword);
    await user.save();
    return {
      status: true,
      message: 'Password changed successfully.',
    };
  }
}

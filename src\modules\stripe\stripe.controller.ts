import {
  Body,
  Controller,
  Delete,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { StripeService } from './stripe.service';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { CurrentUser } from 'src/utils/decorators/custom.decorators';
import { AuthUser } from 'src/utils/utils.types';

@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Post('webhook/card-attached')
  @HttpCode(HttpStatus.OK)
  async cardsetupWebhookEvents(
    @Body() payload: any,
    // @Headers('stripe-signature') sig: string,
  ) {
    await this.stripeService.handleCardAddedEvent(payload);
    return {
      status: true,
    };
  }

  @Post('add-card-intent')
  @UseGuards(AuthGuard)
  async createAddCardPaymentIntent(@CurrentUser() user: AuthUser) {
    return await this.stripeService.createPaymentIntent(user.id);
  }

  @Delete('detach-card/:cardId')
  @UseGuards(AuthGuard)
  async detachCard(
    @CurrentUser() user: AuthUser,
    @Param('cardId') cardId: string,
  ) {
    return await this.stripeService.detachPaymentMethod(user.id, cardId);
  }
}

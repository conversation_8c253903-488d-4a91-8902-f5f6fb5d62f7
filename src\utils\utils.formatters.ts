import { omit, pick } from './utils.functions';

export const getUserData = (user: any) => ({
  id: `${user._id}`,
  ...pick(user, [
    'firstName',
    'lastName',
    'email',
    'phoneNumber',
    'isVerified',
  ]),
});

export const formatLocation = (data?: any) =>
  data
    ? {
        name: data.name,
        lng: data.location.coordinates[0],
        lat: data.location.coordinates[1],
      }
    : data;

export const formatTripRequest = (p: any) =>
  !p.user
    ? p
    : {
        id: p.user._id,
        ...getUserData(p.user),
        ...omit(p.data, ['tripId']),
        requestId: p._id,
        requestedAt: p.createdAt,
      };

export const formatTrip = (trip: any) => {
  return {
    id: trip._id,
    status: trip.status,
    type: trip.type,
    driver: trip.driver?._id
      ? {
          id: trip.driver._id,
          ...getUserData(trip.driver),
          carDetails: trip.driver.verification?.carDetails,
        }
      : trip.driver,
    passengers: trip.passengers?.map((p: any) => formatTripRequest(p)),

    origin: formatLocation(trip.origin),
    destination: formatLocation(trip.destination),
    stops: trip.stops?.map((stop: any) => formatLocation(stop)),
    ...pick(trip, ['timestamp', 'noOfPassengers', 'pricePerSeat', 'mode']),
    preferences: trip.preferences?.map((preference: any) =>
      pick(preference, ['desc', 'value']),
    ),
  };
};

export const formatCards = (data?: any) =>
  data?.map((card) => ({
    id: card._id.toString(),
    ...pick(card, ['last4', 'brand']),
  }));

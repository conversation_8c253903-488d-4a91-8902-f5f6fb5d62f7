import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Trip } from './trip.schema';
import { User } from './user.schema';
import mongoose, { Types } from 'mongoose';
import { REQUEST_STATUS } from 'src/utils/utils.enum';

@Schema({
  timestamps: true,
})
export class TripRequest {
  @Prop({
    type: Types.ObjectId,
    ref: 'Trip',
  })
  trip: Trip;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
  })
  user: User;

  @Prop({
    type: Types.ObjectId,
    ref: 'User',
  })
  driver: User;

  @Prop({
    type: mongoose.Schema.Types.Mixed,
  })
  data: any;

  @Prop({
    default: REQUEST_STATUS.pending,
  })
  status: string;

  @Prop({
    default: 0,
  })
  rating: number;

  @Prop({
    default: 0,
  })
  discount: number;

  @Prop({
    type: Boolean,
    default: false,
  })
  paymentMade: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  pickedup: boolean;

  @Prop({
    type: mongoose.Schema.Types.Date,
  })
  pickupTime: Date;
}

export const TripRequestSchema = SchemaFactory.createForClass(TripRequest);

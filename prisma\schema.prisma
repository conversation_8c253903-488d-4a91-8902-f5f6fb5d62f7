// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("PG_DATABASE_URL")
}

model Wallet {
  id      String @id //same as user id
  balance Float  @default(0)
}

model Transactions {
  ref       String            @id
  type      TransactionType
  amount    Float
  discount  Float?
  userId    String
  driverId  String?
  status    TransactionStatus @default(PENDING)
  tripId    String?
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
  promoId   String?
  promoCode PromoCode?        @relation(fields: [promoId], references: [id])
}

model PromoCode {
  id               String         @id @default(uuid())
  code             String
  discount         Float //in percentage
  maxDiscountValue Float
  usageCount       Int
  usersApplied     Int            @default(0)
  totalDiscount    Float          @default(0)
  expiresAt        DateTime
  createdAt        DateTime       @default(now())
  isActive         Boolean        @default(true)
  transactions     Transactions[]
}

enum TransactionStatus {
  SUCCESS
  FAILED
  PENDING
  REVERSED
}

enum TransactionType {
  CARPOOL_RIDE
  PRIVATE_RIDE
}

import { Test, TestingModule } from '@nestjs/testing';
import { CarpoolController } from './carpool.controller';
import { CarpoolService } from './carpool.service';

describe('CarpoolController', () => {
  let controller: CarpoolController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CarpoolController],
      providers: [CarpoolService],
    }).compile();

    controller = module.get<CarpoolController>(CarpoolController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});

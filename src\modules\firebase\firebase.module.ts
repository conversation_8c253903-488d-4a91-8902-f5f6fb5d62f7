import { Global, Module } from '@nestjs/common';
import { FirebaseService } from './firebase.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { initializeApp } from 'firebase/app';

const FirebaseProvider = {
  provide: 'FIREBASE_APP',
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => {
    const projectId = configService.get<string>('FIREBASE_PROJECT_ID');
    const firebaseConfig = {
      apiKey: configService.get<string>('FIREBASE_API_KEY'),
      authDomain: `${projectId}.firebaseapp.com`,
      projectId,
      storageBucket: `${projectId}.appspot.com`,
      messagingSenderId: '991851298611',
      appId: configService.get<string>('FIREBASE_APP_ID'),
    };
    return initializeApp(firebaseConfig);
  },
};

@Global()
@Module({
  imports: [ConfigModule],
  providers: [FirebaseProvider, FirebaseService],
  exports: [FirebaseService],
})
export class FirebaseModule {}

import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { format } from 'date-fns';
import { BaseResponseDTO } from 'src/utils/utils.types';
import { GetTransactionsDTO } from './dto/transaction.dto';
import { MAX_PAGE_SIZE } from 'src/utils/utils.constant';
import { calculatePaginationData } from 'src/utils/utils.functions';

@Injectable()
export class TransactionService {
  constructor(private readonly prisma: PrismaService) {}

  generateTransactionRef() {
    return format(new Date(), "'COR-'yyyyMMddHHmmssSSS");
  }

  async calculateDiscount(promoId: string, amount: number, usageCount: number) {
    const promoCode = await this.prisma.promoCode.findFirstOrThrow({
      where: {
        id: promoId,
      },
    });

    let discount = usageCount > 0 ? (promoCode.discount * amount) / 100 : 0;
    discount =
      discount > promoCode.maxDiscountValue
        ? promoCode.maxDiscountValue
        : discount;
    return discount;
  }

  async getTransactions(
    userId: string,
    payload: GetTransactionsDTO,
  ): Promise<BaseResponseDTO> {
    const page = payload.page ? +payload.page : 1;
    const take = payload.pageSize ? +payload.pageSize : MAX_PAGE_SIZE;
    const skip = (page - 1) * take;

    const count = await this.prisma.transactions.count({
      where: {
        userId,
      },
    });

    const transactions = await this.prisma.transactions.findMany({
      where: {
        userId,
      },
      skip,
      take,
    });

    return {
      status: true,
      message: 'OK',
      type: 'array',
      data: transactions,
      pagination: calculatePaginationData(count, take, page),
    };
  }
}

import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, RootFilterQuery } from 'mongoose';
import { User } from 'src/schemas/user.schema';
import { BaseResponseDTO } from 'src/utils/utils.types';
import {
  CreatePromoCodeDTO,
  GetAccountsDTO,
  GetTripsDTO,
} from './dto/admin.dto';
import { MAX_PAGE_SIZE } from 'src/utils/utils.constant';
import { ROLE, VERIFICATION_STATUS } from 'src/utils/utils.enum';
import { calculatePaginationData, omit } from 'src/utils/utils.functions';
import { formatLocation, formatTrip } from 'src/utils/utils.formatters';
import { Trip } from 'src/schemas/trip.schema';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AdminService {
  private readonly userModel: Model<User>;
  private readonly tripModel: Model<Trip>;

  constructor(
    @InjectModel(User.name) userModel: Model<User>,
    @InjectModel(Trip.name) tripModel: Model<Trip>,
    private readonly prisma: PrismaService,
  ) {
    this.userModel = userModel;
    this.tripModel = tripModel;
  }

  async getAccounts(payload: GetAccountsDTO): Promise<BaseResponseDTO> {
    const page = payload.page ? +payload.page : 1;
    const take = payload.pageSize ? +payload.pageSize : MAX_PAGE_SIZE;
    const skip = (page - 1) * take;

    let query: RootFilterQuery<User>;

    if (payload.type === ROLE.user) {
      query = {
        verificationStatus: {
          $in: [VERIFICATION_STATUS.not_started, VERIFICATION_STATUS.declined],
        },
      };
    } else {
      query = {
        verificationStatus: {
          $in: [VERIFICATION_STATUS.pending, VERIFICATION_STATUS.approved],
        },
      };
    }
    const count = await this.userModel.countDocuments(query);
    const accounts = await this.userModel
      .find(query)
      .limit(take)
      .skip(skip)
      .lean();

    return {
      status: true,
      message: 'OK',
      type: 'array',
      data: accounts.map((account) => ({
        id: account._id,
        ...omit(account, ['__v', '_id', 'lastLocation']),
        lastLocation: formatLocation(account.lastLocation),
      })),
      pagination: calculatePaginationData(count, take, page),
    };
  }

  async banUser(userId: string): Promise<BaseResponseDTO> {
    const user = await this.userModel.findById(userId);

    if (!user) {
      throw new BadRequestException('user with the id not found.');
    }

    if (user.isBanned) {
      throw new BadRequestException('user already banned.');
    }

    user.isBanned = true;
    await user.save();

    return {
      status: true,
      message: 'user banned successfully.',
      type: 'object',
      data: {
        userId,
      },
    };
  }

  async getTrips(payload: GetTripsDTO): Promise<BaseResponseDTO> {
    const page = payload.page ? +payload.page : 1;
    const take = payload.pageSize ? +payload.pageSize : MAX_PAGE_SIZE;
    const skip = (page - 1) * take;
    const query: RootFilterQuery<Trip> = {
      createdAt: {
        $gte: new Date(0),
        $lte: new Date(),
      },
    };

    if (payload.type) {
      query.mode = payload.type;
    }

    if (payload.status) {
      query.status = payload.status;
    }

    if (payload.from) {
      query.createdAt.$gte = new Date(payload.from);
    }
    if (payload.to) {
      query.createdAt.$lte = new Date(payload.to);
    }

    if (payload.priceRange) {
      const [minPrice, maxPrice] = payload.priceRange.split('-');
      query.pricePerSeat = {
        $gte: +minPrice,
        $lte: +maxPrice,
      };
    }

    const count = await this.tripModel.countDocuments(query);
    const trips = await this.tripModel
      .find(query)
      .limit(take)
      .skip(skip)
      .populate([
        'driver',
        {
          path: 'passengers',
          populate: 'user',
        },
      ])
      .lean();

    return {
      status: true,
      message: 'OK',
      type: 'array',
      data: trips.map((trip) => formatTrip(trip)),
      pagination: calculatePaginationData(count, take, page),
    };
  }

  async createPromoCode(payload: CreatePromoCodeDTO): Promise<BaseResponseDTO> {
    const promoCode = await this.prisma.promoCode.create({
      data: payload,
    });

    return {
      status: true,
      message: 'Promo code created successfully.',
      type: 'object',
      data: promoCode,
    };
  }
}

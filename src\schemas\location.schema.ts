import { Prop, raw, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema()
export class Place {
  @Prop(
    raw({
      type: {
        type: String,
        enum: ['Point'],
        default: 'Point',
      },
      coordinates: [Number],
    }),
  )
  location: Record<string, any>;

  @Prop({
    type: String,
  })
  name?: string;
}

export const PlaceSchema = SchemaFactory.createForClass(Place);
PlaceSchema.index({
  location: '2dsphere',
});

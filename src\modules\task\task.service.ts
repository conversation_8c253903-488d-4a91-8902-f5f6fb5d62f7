import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { subMinutes } from 'date-fns';
import { Model } from 'mongoose';
import { TripRequest } from 'src/schemas/trip-request.schema';
import { Trip } from 'src/schemas/trip.schema';
import { REQUEST_STATUS, TRIP_STATUS } from 'src/utils/utils.enum';

@Injectable()
export class TaskService {
  private readonly logger = new Logger(TaskService.name);
  private readonly tripRequestModel: Model<TripRequest>;
  private readonly tripModel: Model<Trip>;

  constructor(
    @InjectModel(TripRequest.name) tripRequestModel: Model<TripRequest>,
    @InjectModel(Trip.name) tripModel: Model<Trip>,
  ) {
    this.tripRequestModel = tripRequestModel;
    this.tripModel = tripModel;
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async deleteStalledTripRequests() {
    await this.tripRequestModel.deleteMany({
      createdAt: {
        $lte: subMinutes(new Date(), 10),
      },
      status: {
        $ne: REQUEST_STATUS.accepted,
      },
    });
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async deleteStalledTrip() {
    await this.tripModel.deleteMany({
      createdAt: {
        $lte: subMinutes(new Date(), 20),
      },
      status: TRIP_STATUS.pending,
    });
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CarpoolService } from './carpool.service';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { CurrentUser } from 'src/utils/decorators/custom.decorators';
import { AuthUser } from 'src/utils/utils.types';
import {
  BookRideDTO,
  RequestIDDTO,
  ScheduleTripDTO,
  GetCoRideDTO,
  RateTripDTO,
  BookPrivateRideDTO,
  TripIDDTO,
  ConfirmPaymentDTO,
  GetCorideRequestDTO,
} from './dto/carpool.dto';
import { Roles } from 'src/utils/decorators/roles.decorator';
import { ROLE } from 'src/utils/utils.enum';
import { RolesGuard } from 'src/utils/gaurds/roles.gaurd';

@Controller('carpool')
@UseGuards(AuthGuard)
export class CarpoolController {
  constructor(private readonly carpoolService: CarpoolService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  async getCorides(@Body() payload: GetCoRideDTO) {
    return await this.carpoolService.getCoRides(payload);
  }

  @Post('schedule')
  @Roles(ROLE.rider)
  @UseGuards(RolesGuard)
  async scheduleTrip(
    @CurrentUser() user: AuthUser,
    @Body() payload: ScheduleTripDTO,
  ) {
    return await this.carpoolService.scheduleTrip(user.id, payload);
  }

  @Post('requests')
  async bookCoRide(
    @CurrentUser() user: AuthUser,
    @Body() payload: BookRideDTO,
  ) {
    return await this.carpoolService.requestRide(user.id, payload);
  }

  @Post('private/requests')
  async bookPrivatRide(
    @CurrentUser() user: AuthUser,
    @Body() payload: BookPrivateRideDTO,
  ) {
    return await this.carpoolService.requestPrivateRide(user.id, payload);
  }

  @Get('requests')
  async getCorideRequests(
    @CurrentUser() driver: AuthUser,
    @Query() query: GetCorideRequestDTO,
  ) {
    return await this.carpoolService.getCorideRequests(driver.id, query);
  }

  @Post('requests/accept')
  @HttpCode(HttpStatus.OK)
  async acceptCorideRequest(
    @CurrentUser() driver: AuthUser,
    @Body() payload: RequestIDDTO,
  ) {
    return await this.carpoolService.acceptCorideRequest(
      payload.requestId,
      driver.id,
    );
  }

  @Post('requests/decline')
  @HttpCode(HttpStatus.OK)
  async declineCorideRequest(
    @CurrentUser() driver: AuthUser,
    @Body() payload: RequestIDDTO,
  ) {
    return await this.carpoolService.declineCorideRequest(
      payload.requestId,
      driver.id,
    );
  }

  @Delete(':tripId/cancel')
  @Roles(ROLE.rider)
  @UseGuards(RolesGuard)
  async cancelTrip(
    @CurrentUser() driver: AuthUser,
    @Param('tripId') tripId: string,
  ) {
    return await this.carpoolService.cancelTrip(driver.id, tripId);
  }

  @Post(':tripId/rate')
  async rateTrip(
    @CurrentUser() user: AuthUser,
    @Param('tripId') tripId: string,
    @Body() payload: RateTripDTO,
  ) {
    return await this.carpoolService.rateTrip(user.id, tripId, payload.rating);
  }

  @Post('pay')
  @HttpCode(HttpStatus.OK)
  async payForTrip(@CurrentUser() user: AuthUser, @Body() payload: TripIDDTO) {
    return await this.carpoolService.payForTrip(user.id, payload.tripId);
  }

  @Post('confirm-payment')
  @Roles(ROLE.rider)
  @UseGuards(RolesGuard)
  @HttpCode(HttpStatus.OK)
  async confirmPayment(
    @CurrentUser() driver: AuthUser,
    @Body() payload: ConfirmPaymentDTO,
  ) {
    return await this.carpoolService.confirmPayment(driver.id, payload);
  }

  @Post('end-trip')
  @Roles(ROLE.rider)
  @UseGuards(RolesGuard)
  @HttpCode(HttpStatus.OK)
  async endTrip(@CurrentUser() driver: AuthUser, @Body() payload: TripIDDTO) {
    return await this.carpoolService.endTrip(driver.id, payload.tripId);
  }

  @Post('confirm-pickup')
  @Roles(ROLE.rider)
  @UseGuards(RolesGuard)
  @HttpCode(HttpStatus.OK)
  async confirmPickUp(
    @CurrentUser() driver: AuthUser,
    @Body() payload: RequestIDDTO,
  ) {
    return await this.carpoolService.confirmPickUp(
      driver.id,
      payload.requestId,
    );
  }
}

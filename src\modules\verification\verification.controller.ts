import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { VerificationService } from './verification.service';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { RolesGuard } from 'src/utils/gaurds/roles.gaurd';
import { Roles } from 'src/utils/decorators/roles.decorator';
import { ROLE } from 'src/utils/utils.enum';
import { UserIdDTO } from 'src/utils/utils.dto';

@Controller('verification')
@Roles(ROLE.admin)
@UseGuards(AuthGuard, RolesGuard)
export class VerificationController {
  constructor(private readonly verificationService: VerificationService) {}

  @Get('requests')
  async getVerificationRequests() {
    return await this.verificationService.getVerificationRequests();
  }

  @Post('approve')
  async markUserAsVerified(@Body() payload: UserIdDTO) {
    return await this.verificationService.markUserAsVerified(payload.userId);
  }
}

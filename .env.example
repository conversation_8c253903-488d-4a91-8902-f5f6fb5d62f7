# Server Configuration
PORT=3000
NODE_ENV=development

# Database URLs
DATABASE_URL=mongodb://localhost:27017/coride
PG_DATABASE_URL=postgresql://username:password@localhost:5432/coride_transactions

# JWT Secret (generate a random string)
JWT_SECRET=your-super-secret-jwt-key-here

# Stripe Configuration (get from Stripe Dashboard)
STRIPE_API_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Firebase Configuration (get from Firebase Console)
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_APP_ID=your-firebase-app-id

# Ably Configuration (get from Ably Dashboard)
ABLY_API_KEY=your-ably-api-key
ABLY_SERVER_KEY=your-ably-server-key

# Termii Configuration (for SMS/Email OTP)
TERMII_BASE_URL=https://api.ng.termii.com
TERMII_API_KEY=your-termii-api-key
TERMII_EMAIL_CONFIGURATION_ID=your-email-config-id

import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsOptional,
  IsString,
  Max,
  <PERSON>,
} from 'class-validator';

export class Location {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsNumber()
  @IsNotEmpty()
  @Min(-90)
  @Max(90)
  lat: number;

  @IsNumber()
  @IsNotEmpty()
  @Min(-180)
  @Max(180)
  lng: number;
}

export class RidePreferences {
  @IsString()
  @IsNotEmpty()
  desc: string;

  @IsBoolean()
  @IsNotEmpty()
  value: boolean;
}

export class UserIdDTO {
  @IsString()
  @IsNotEmpty()
  userId: string;
}

export class PaginationRequestDTO {
  @IsNumberString()
  @IsOptional()
  page: number;

  @IsNumberString()
  @IsOptional()
  pageSize: number;
}

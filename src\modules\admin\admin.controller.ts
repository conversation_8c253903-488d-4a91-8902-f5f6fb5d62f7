import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { Roles } from 'src/utils/decorators/roles.decorator';
import { ROLE } from 'src/utils/utils.enum';
import { RolesGuard } from 'src/utils/gaurds/roles.gaurd';
import {
  CreatePromoCodeDTO,
  GetAccountsDTO,
  GetTripsDTO,
  UserIDDTO,
} from './dto/admin.dto';

@Controller('admin')
@Roles(ROLE.admin)
@UseGuards(AuthGuard, RolesGuard)
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get('accounts')
  async getAccounts(@Query() query: GetAccountsDTO) {
    return await this.adminService.getAccounts(query);
  }

  @Delete('accounts/:userId/ban')
  @HttpCode(HttpStatus.OK)
  async banUser(@Param() payload: UserIDDTO) {
    return await this.adminService.banUser(payload.userId);
  }

  @Get('trips')
  async getTrips(@Query() query: GetTripsDTO) {
    return await this.adminService.getTrips(query);
  }

  @Post('promo-code')
  async createPromoCode(@Body() payload: CreatePromoCodeDTO) {
    return await this.adminService.createPromoCode(payload);
  }
}

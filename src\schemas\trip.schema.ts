import { Prop, raw, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';
import { User } from './user.schema';
import { Type } from 'class-transformer';
import { Place, PlaceSchema } from './location.schema';
import { TRIP_MODE, TRIP_STATUS } from 'src/utils/utils.enum';

export type TripDocument = HydratedDocument<Trip>;

@Schema({
  timestamps: true,
})
export class Trip {
  @Prop({
    type: mongoose.Schema.ObjectId,
    ref: User.name,
    required: true,
  })
  driver: User;

  @Prop({
    type: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: User.name,
      },
    ],
  })
  passengers: User[];

  @Prop({
    required: true,
  })
  type: string;

  @Prop({
    type: PlaceSchema,
  })
  @Type(() => Place)
  origin: Place;

  @Prop({
    type: PlaceSchema,
  })
  @Type(() => Place)
  destination: Place;

  @Prop({
    type: [PlaceSchema],
    default: [],
  })
  @Type(() => Place)
  stops: Place[];

  @Prop()
  timestamp: string;

  @Prop({
    required: true,
  })
  noOfPassengers: number;

  @Prop({
    required: true,
  })
  pricePerSeat: number;

  @Prop({
    type: [
      raw({
        desc: {
          type: String,
        },
        value: {
          type: Boolean,
        },
      }),
    ],
  })
  preferences: Record<string, any>[];

  @Prop({
    default: TRIP_STATUS.pending,
  })
  status: string;

  @Prop({
    default: TRIP_MODE.carpool,
  })
  mode: string;
}

export const TripSchema = SchemaFactory.createForClass(Trip);

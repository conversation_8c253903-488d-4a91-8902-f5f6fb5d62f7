import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './modules/auth/auth.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { UserModule } from './modules/user/user.module';
import { JwtModule } from '@nestjs/jwt';
import { FileSystemStoredFile, NestjsFormDataModule } from 'nestjs-form-data';
import { FirebaseModule } from './modules/firebase/firebase.module';
import { PrismaModule } from './modules/prisma/prisma.module';
import { CarpoolModule } from './modules/carpool/carpool.module';
import { VerificationModule } from './modules/verification/verification.module';
import { AblyModule } from './modules/ably/ably.module';
import { StripeModule } from './modules/stripe/stripe.module';
import { TransactionModule } from './modules/transaction/transaction.module';
import { AdminModule } from './modules/admin/admin.module';
import { ScheduleModule } from '@nestjs/schedule';
import { TaskModule } from './modules/task/task.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRoot(process.env.DATABASE_URL),
    NestjsFormDataModule.config({
      storage: FileSystemStoredFile,
      isGlobal: true,
    }),
    JwtModule.register({
      global: true,
    }),
    ScheduleModule.forRoot(),
    UserModule,
    AuthModule,
    FirebaseModule,
    PrismaModule,
    CarpoolModule,
    VerificationModule,
    AblyModule,
    StripeModule,
    TransactionModule,
    AdminModule,
    TaskModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

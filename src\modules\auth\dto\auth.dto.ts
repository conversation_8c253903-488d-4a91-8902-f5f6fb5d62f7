import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumberString,
  IsString,
} from 'class-validator';

export class SignInDTO {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class SignUpDTO extends SignInDTO {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsNumberString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsBoolean()
  @IsNotEmpty()
  hasPersonalVehicle: boolean;
}

export class SendOTPDTO {
  @IsNumberString()
  @IsNotEmpty()
  phoneNumber: string;
}

export class VerifyOTPDTO {
  @IsString()
  @IsNotEmpty()
  pin: string;

  @IsString()
  @IsNotEmpty()
  pin_id: string;
}

export class VerifyPasswordResetOTPDTO {
  @IsString()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  pin: string;
}

export class ResetPasswordDTO {
  @IsString()
  @IsNotEmpty()
  newPassword: string;
}

export class ChangePasswordDTO extends ResetPasswordDTO {
  @IsString()
  @IsNotEmpty()
  oldPassword: string;
}

export class GoogleAuthDTO {
  @IsString()
  @IsNotEmpty()
  accessToken: string;
}

import { Modu<PERSON> } from '@nestjs/common';
import { TaskService } from './task.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  TripRequest,
  TripRequestSchema,
} from 'src/schemas/trip-request.schema';
import { Trip, TripSchema } from 'src/schemas/trip.schema';

@Module({
  providers: [TaskService],
  imports: [
    MongooseModule.forFeature([
      {
        name: TripRequest.name,
        schema: TripRequestSchema,
      },
      {
        name: Trip.name,
        schema: TripSchema,
      },
    ]),
  ],
  exports: [TaskService],
})
export class TaskModule {}

import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { addMinutes } from 'date-fns';

@Schema()
export class Token {
  @Prop({
    required: true,
  })
  pin: string;

  @Prop({
    unique: true,
    required: true,
  })
  email: string;

  @Prop({
    default: new Date(),
  })
  createdAt: Date;

  @Prop({
    default: addMinutes(new Date(), 10),
  })
  expiresAt: Date;
}

export const TokenSchema = SchemaFactory.createForClass(Token);

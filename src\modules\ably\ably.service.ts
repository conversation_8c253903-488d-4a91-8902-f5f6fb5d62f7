import { Inject, Injectable } from '@nestjs/common';
import Ably from 'ably';
import { InjectModel } from '@nestjs/mongoose';
import { User } from 'src/schemas/user.schema';
import { Model } from 'mongoose';

@Injectable()
export class AblyService {
  private readonly ably: Ably.Realtime;
  private readonly userModel: Model<User>;

  constructor(
    @Inject('ABLY_APP') ablyApp: Ably.Realtime,
    @InjectModel(User.name) userModel: Model<User>,
  ) {
    this.ably = ablyApp;
    this.userModel = userModel;
  }

  async createTokenRequest(clientId: string) {
    return await this.ably.auth.createTokenRequest({
      clientId,
    });
  }

  async publishEvent(channel: string, eventName: string, data: any) {
    await this.ably.channels.get(channel).publish(eventName, data);
  }

  async processPresenceEvent(body: any) {
    body.items.forEach(async (item: any) => {
      const messages = await Ably.Realtime.PresenceMessage.fromEncodedArray(
        item.data.presence,
      );
      messages.forEach(async (message) => {
        try {
          await this.userModel.findByIdAndUpdate(message.clientId, {
            isOnline: message.action === 'enter',
            lastSeen: new Date(message.timestamp).toISOString(),
          });
        } catch {
          //do nothing
        }
      });
    });
  }
}

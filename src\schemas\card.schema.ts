import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type CardDocument = HydratedDocument<Card>;

@Schema()
export class Card {
  @Prop({
    required: true,
  })
  brand: string;

  @Prop({
    required: true,
  })
  last4: string;

  @Prop({
    required: true,
  })
  payment_method: string;
}

export const CardSchema = SchemaFactory.createForClass(Card);

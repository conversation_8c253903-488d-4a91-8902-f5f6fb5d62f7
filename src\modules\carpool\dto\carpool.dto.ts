import { Type } from 'class-transformer';
import {
  IsDateString,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsString,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { Location, RidePreferences } from 'src/utils/utils.dto';
import { PAYMENT_MODE, TRIP_MODE, TRIP_TYPE } from 'src/utils/utils.enum';

export class ScheduleTripDTO {
  @IsString()
  @IsNotEmpty()
  @IsIn(Object.values(TRIP_TYPE))
  type: TRIP_TYPE;

  @IsNotEmpty()
  @Type(() => Location)
  @ValidateNested()
  origin: Location;

  @IsNotEmpty()
  @Type(() => Location)
  @ValidateNested()
  destination: Location;

  @Type(() => Location)
  @ValidateNested({
    each: true,
  })
  stops: Location[];

  @IsNotEmpty()
  @IsDateString()
  timestamp: string;

  @IsNotEmpty()
  @IsInt()
  noOfPassengers: number;

  @Type(() => RidePreferences)
  @ValidateNested({
    each: true,
  })
  preferences: RidePreferences[];

  @IsNumber()
  @IsNotEmpty()
  pricePerSeat: number;
}

export class TargetsDTO {
  @IsNotEmpty()
  @Type(() => Location)
  @ValidateNested()
  pickup: Location;

  @IsNotEmpty()
  @Type(() => Location)
  @ValidateNested()
  dropoff: Location;
}

class BaseTripRequestDTO extends TargetsDTO {
  @IsNotEmpty()
  @Type(() => Location)
  @ValidateNested()
  currentLocation: Location;

  @IsString()
  @IsNotEmpty()
  @IsIn(Object.values(PAYMENT_MODE))
  modeOfPayment: PAYMENT_MODE;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => o.modeOfPayment === PAYMENT_MODE.card)
  cardId: string;
}

export class TripIDDTO {
  @IsString()
  @IsNotEmpty()
  tripId: string;
}

export class ConfirmPaymentDTO extends TripIDDTO {
  @IsString()
  @IsNotEmpty()
  requestId: string;
}

export class BookRideDTO extends BaseTripRequestDTO {
  @IsString()
  @IsNotEmpty()
  tripId: string;
}

export class BookPrivateRideDTO extends BaseTripRequestDTO {
  @IsString()
  @IsNotEmpty()
  driverId: string;

  @IsNumber()
  @IsNotEmpty()
  estimatedPrice: number;
}

export class GetCoRideDTO extends TargetsDTO {
  @IsString()
  @IsNotEmpty()
  @IsIn(Object.values(TRIP_MODE))
  mode: TRIP_MODE;
}

export class RequestIDDTO {
  @IsString()
  @IsNotEmpty()
  requestId: string;
}

export class RateTripDTO {
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsNotEmpty()
  rating: number;
}

export class GetCorideRequestDTO {
  @IsEnum(TRIP_MODE)
  @IsNotEmpty()
  mode: TRIP_MODE;

  @IsString()
  @IsNotEmpty()
  @ValidateIf((o) => o.mode === TRIP_MODE.carpool)
  tripId: string;
}

import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { CurrentUser } from 'src/utils/decorators/custom.decorators';
import { AuthUser } from 'src/utils/utils.types';
import { GetTransactionsDTO } from './dto/transaction.dto';

@Controller('transactions')
@UseGuards(AuthGuard)
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Get()
  async getTransactions(
    @CurrentUser() user: AuthUser,
    @Query() query: GetTransactionsDTO,
  ) {
    return await this.transactionService.getTransactions(user.id, query);
  }
}

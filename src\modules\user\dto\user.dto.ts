import {
  IsArray,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumberString,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IDENTITY_TYPE, VERIFICATION_IMAGE_TYPE } from 'src/utils/utils.enum';
import {
  FileSystemStoredFile,
  HasMimeType,
  IsFile,
  MaxFileSize,
} from 'nestjs-form-data';
import { Location, PaginationRequestDTO } from 'src/utils/utils.dto';

export class AddressDTO {
  @IsArray()
  @ValidateNested({
    each: true,
  })
  @Type(() => Location)
  addresses: Location[];
}

export class VehicleDetailsDTO {
  @IsNotEmpty()
  @IsString()
  make: string;

  @IsNotEmpty()
  @IsString()
  model: string;

  @IsNotEmpty()
  @IsString()
  colour: string;

  @IsNotEmpty()
  @IsInt()
  yearOfManufacture: number;

  @IsNotEmpty()
  @IsString()
  chassisNumber: string;

  @IsNotEmpty()
  @IsInt()
  passengerCapacity: number;

  @IsNotEmpty()
  @IsString()
  plateNumber: string;
}

export class IdentityDTO {
  @IsNotEmpty()
  @IsIn(Object.values(IDENTITY_TYPE))
  type: IDENTITY_TYPE;

  @IsNotEmpty()
  @IsNumberString()
  value: string;
}

export class VerificationImageDTO {
  @IsNotEmpty()
  @IsIn(Object.values(VERIFICATION_IMAGE_TYPE))
  type: VERIFICATION_IMAGE_TYPE;

  @IsNotEmpty()
  @IsFile()
  @HasMimeType(['image/jpeg', 'image/png'], {}, { each: true })
  @MaxFileSize(1e7, {
    each: true,
  })
  image: FileSystemStoredFile;
}

export class GetRideDTO extends PaginationRequestDTO {
  @IsNotEmpty()
  @IsString()
  @IsIn(['taken', 'given'])
  role: string;
}

export class ApplyPromoCodeDTO {
  @IsNotEmpty()
  @IsString()
  code: string;
}

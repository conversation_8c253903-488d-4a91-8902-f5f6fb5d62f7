import { forwardRef, Module } from '@nestjs/common';
import { CarpoolService } from './carpool.service';
import { CarpoolController } from './carpool.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Trip, TripSchema } from 'src/schemas/trip.schema';
import { UserModule } from 'src/modules/user/user.module';
import {
  TripRequest,
  TripRequestSchema,
} from 'src/schemas/trip-request.schema';
import { StripeModule } from '../stripe/stripe.module';
import { Card, CardSchema } from 'src/schemas/card.schema';
import { TransactionModule } from '../transaction/transaction.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Trip.name,
        schema: TripSchema,
      },
      {
        name: TripRequest.name,
        schema: TripRequestSchema,
      },
      {
        name: Card.name,
        schema: CardSchema,
      },
    ]),
    forwardRef(() => UserModule),
    StripeModule,
    TransactionModule,
  ],
  controllers: [CarpoolController],
  providers: [CarpoolService],
  exports: [CarpoolService],
})
export class CarpoolModule {}

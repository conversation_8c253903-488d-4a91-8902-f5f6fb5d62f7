export interface PaginationControl {
  page: number;
  pageSize: number;
  totalRecords: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface BaseResponseDTO {
  status: boolean;
  message: string;
  type?: 'array' | 'object';
  data?: any;
  pagination?: PaginationControl;
}

export interface AuthUser {
  id: string;
  role: string;
  email: string;
}

export type ChargeCardData = {
  paymentMethod: string;
  customerId: string;
  amount: number;
};

export type CreateStripeCustomerData = {
  name: string;
  phone?: string;
  email: string;
};

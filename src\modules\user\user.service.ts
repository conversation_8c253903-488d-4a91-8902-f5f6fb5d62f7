import {
  BadRequestException,
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User } from 'src/schemas/user.schema';
import {
  AddressDTO,
  GetRideDTO,
  IdentityDTO,
  VehicleDetailsDTO,
  VerificationImageDTO,
} from './dto/user.dto';
import {
  AuthUser,
  BaseResponseDTO,
  PaginationControl,
} from 'src/utils/utils.types';
import { calculatePaginationData, omit } from 'src/utils/utils.functions';
import { FirebaseService } from 'src/modules/firebase/firebase.service';
import {
  REQUEST_STATUS,
  STORAGE_DIRECTORY,
  TRIP_STATUS,
  VERIFICATION_STATUS,
} from 'src/utils/utils.enum';
import { CarpoolService } from '../carpool/carpool.service';
import {
  formatCards,
  formatLocation,
  formatTrip,
} from 'src/utils/utils.formatters';
import { PrismaService } from '../prisma/prisma.service';
import { MAX_PAGE_SIZE } from 'src/utils/utils.constant';
import { Location } from 'src/utils/utils.dto';

@Injectable()
export class UserService {
  readonly model: Model<User>;
  constructor(
    @InjectModel(User.name) userModel: Model<User>,
    private readonly firebase: FirebaseService,
    private readonly prisma: PrismaService,
    @Inject(forwardRef(() => CarpoolService))
    private readonly carpoolService: CarpoolService,
  ) {
    this.model = userModel;
  }

  async getProfile(userId: string): Promise<BaseResponseDTO> {
    const user = await this.model.findById(userId).populate('cards').lean();

    const wallet = await this.prisma.wallet.findFirst({
      where: {
        id: userId,
      },
      select: {
        balance: true,
      },
    });

    if (user.promoCode) {
      const promoCode = await this.prisma.promoCode.findFirstOrThrow({
        where: {
          id: user.promoCode.id,
        },
        select: {
          discount: true,
          maxDiscountValue: true,
          code: true,
          expiresAt: true,
          usageCount: true,
        },
      });

      user.promoCode = {
        ...user.promoCode,
        ...promoCode,
        usageLeft: user.promoCode.usageCount,
      };
    }

    return {
      status: true,
      message: 'Profile retrieved successfully.',
      type: 'object',
      data: {
        id: user._id,
        ...omit(user, ['password', '__v', '_id']),
        lastLocation: formatLocation(user.lastLocation),
        cards: formatCards(user.cards),
        wallet,
      },
    };
  }

  async saveAddresses(
    userId: string,
    payload: AddressDTO,
  ): Promise<BaseResponseDTO> {
    await this.model.findByIdAndUpdate(userId, {
      addresses: payload.addresses,
    });

    return {
      status: true,
      message: 'Address updated.',
    };
  }

  async updateVehicleDetails(
    userId: string,
    payload: VehicleDetailsDTO,
  ): Promise<BaseResponseDTO> {
    const user = await this.model.findById(userId);

    await this.model.findByIdAndUpdate(userId, {
      verification: {
        ...user?.verification,
        carDetails: payload,
      },
    });
    return {
      status: true,
      message: 'Vehicle details updated.',
    };
  }

  async uploadVerificationImage(
    userId: string,
    payload: VerificationImageDTO,
  ): Promise<BaseResponseDTO> {
    const user = await this.model.findById(userId);

    if (user.verificationStatus === VERIFICATION_STATUS.pending) {
      throw new BadRequestException('Verification pending approval.');
    }

    if (user.verificationStatus === VERIFICATION_STATUS.approved) {
      throw new BadRequestException('Verification already completed.');
    }

    if (user.verification[payload.type]) {
      await this.firebase.deleteFile(user.verification[payload.type]);
    }

    if (user.verificationStatus !== VERIFICATION_STATUS.inProgress) {
      user.verificationStatus = VERIFICATION_STATUS.inProgress;
    }

    const url = await this.firebase.uploadFile(
      userId,
      payload.image,
      STORAGE_DIRECTORY.verification,
      payload.type,
    );

    user.verification[payload.type] = url;
    await user.save();

    return {
      status: true,
      message: 'Image uploaded.',
    };
  }

  async updateIdentity(
    userId: string,
    payload: IdentityDTO,
  ): Promise<BaseResponseDTO> {
    const user = await this.model.findById(userId);

    if (user.verificationStatus === VERIFICATION_STATUS.pending) {
      throw new BadRequestException('Verification pending approval.');
    }

    if (user.verificationStatus === VERIFICATION_STATUS.approved) {
      throw new BadRequestException('Verification already completed.');
    }

    if (user.verificationStatus !== VERIFICATION_STATUS.inProgress) {
      user.verificationStatus = VERIFICATION_STATUS.inProgress;
    }

    user.verification.identity = payload;
    await user.save();

    return {
      status: true,
      message: 'Identity updated.',
    };
  }

  async requestVerification(userId: string): Promise<BaseResponseDTO> {
    const user = await this.model.findById(userId);

    if (user.verificationStatus === VERIFICATION_STATUS.pending) {
      throw new BadRequestException('Verification pending approval.');
    }

    const verificationForm: any = {
      carDetails: true,
      identity: true,
      driverLicense_front: true,
      driverLicense_back: true,
      vehicleLicense: true,
      motorInsurance: true,
      roadWorthiness: true,
    };

    if (!user.verification.carDetails?.make) {
      verificationForm.carDetails = false;
    }
    if (!user.verification.driverLicense_front) {
      verificationForm.driverLicense_front = false;
    }
    if (!user.verification.driverLicense_back) {
      verificationForm.driverLicense_back = false;
    }
    if (!user.verification.motorInsurance) {
      verificationForm.motorInsurance = false;
    }
    if (!user.verification.vehicleLicense) {
      verificationForm.vehicleLicense = false;
    }
    if (!user.verification.roadWorthiness) {
      verificationForm.roadWorthiness = false;
    }
    if (!user.verification.identity?.type) {
      verificationForm.identity = false;
    }

    const status = !Object.values(verificationForm).some((v) => v === false);

    if (status) {
      user.verificationStatus = VERIFICATION_STATUS.pending;
      await user.save();
    }
    return {
      status,
      message: status
        ? 'Verification process started.'
        : 'Incomplete verification data.',
      type: 'object',
      data: verificationForm,
    };
  }

  async getPendingOrOngoingTrip(user: AuthUser): Promise<BaseResponseDTO> {
    const trip: any = await this.carpoolService.tripModel
      .findOne({
        status: {
          $in: [TRIP_STATUS.pending, TRIP_STATUS.ongoing],
        },
        $or: [
          {
            driver: new Types.ObjectId(user.id),
          },
          {
            passengers: {
              $in: [new Types.ObjectId(user.id)],
            },
          },
        ],
      })
      .populate('driver')
      .sort({ createdAt: -1 })
      .lean();

    if (!trip) {
      return {
        status: true,
        message: 'no active trip found.',
      };
    }

    trip.passengers = await this.carpoolService.getTripRequests(trip._id);
    return {
      status: true,
      message: 'OK',
      type: 'object',
      data: formatTrip(trip),
    };
  }

  async activatePrivateRide(
    driverId: string,
    isActive: boolean,
  ): Promise<BaseResponseDTO> {
    const user = await this.model.findById(driverId);

    if (isActive && user.privateRideActive) {
      throw new BadRequestException('private ride mode already activated.');
    }

    if (!isActive && !user.privateRideActive) {
      throw new BadRequestException('private ride mode already deactivated.');
    }

    const existingTrip = await this.carpoolService.getActiveTrip(driverId);

    if (existingTrip && isActive) {
      throw new BadRequestException(
        `account has a ${existingTrip.status} trip. Cancel or complete the trip to schedule another trip.`,
      );
    }

    user.privateRideActive = isActive;
    await user.save();

    return {
      status: true,
      message: `private ride mode ${isActive ? 'activated' : 'deactivated'}.`,
    };
  }

  async getRides(
    userId: string,
    payload: GetRideDTO,
  ): Promise<BaseResponseDTO> {
    const page = payload.page ? +payload.page : 1;
    const take = payload.pageSize ? +payload.pageSize : MAX_PAGE_SIZE;
    const skip = (page - 1) * take;
    let data: any;
    let pagination: PaginationControl;

    if (payload.role === 'taken') {
      const count = await this.carpoolService.tripRequestModel.countDocuments({
        user: new Types.ObjectId(userId),
        status: REQUEST_STATUS.accepted,
      });

      const requests = await this.carpoolService.tripRequestModel
        .find({
          user: new Types.ObjectId(userId),
          status: REQUEST_STATUS.accepted,
        })
        .skip(skip)
        .limit(take)
        .populate({
          path: 'trip',
          populate: [
            {
              path: 'driver',
            },
          ],
        })
        .lean();

      data = requests.map((req) => ({
        requesData: {
          id: req._id,
          ...omit(req as any, ['trip', 'driver', '_id', '__v']),
        },
        tripData: omit(formatTrip(req.trip), ['passengers']),
      }));

      pagination = calculatePaginationData(count, take, page);
    } else {
      const count = await this.carpoolService.tripModel.countDocuments({
        driver: new Types.ObjectId(userId),
        status: TRIP_STATUS.completed,
      });

      const trips = await this.carpoolService.tripModel
        .find({
          driver: new Types.ObjectId(userId),
          status: TRIP_STATUS.completed,
        })
        .limit(take)
        .skip(skip)
        .populate('passengers');

      data = trips.map((trip) => omit(formatTrip(trip), ['driver']));
      pagination = calculatePaginationData(count, take, page);
    }

    return {
      status: true,
      message: 'Ok',
      type: 'array',
      data,
      pagination,
    };
  }

  async applyPromoCode(code: string, userId: string): Promise<BaseResponseDTO> {
    const promoCode = await this.prisma.promoCode.findFirst({
      where: {
        code,
        isActive: true,
      },
    });
    if (!promoCode) {
      throw new BadRequestException('Invalid or expired code.');
    }

    const user = await this.model.findById(userId);
    if (user.promoCode?.id === promoCode.id) {
      throw new ConflictException('promo code already applied.');
    }

    user.promoCode = {
      id: promoCode.id,
      usageCount: promoCode.usageCount,
    };
    await user.save();

    await this.prisma.promoCode.update({
      where: {
        id: promoCode.id,
      },
      data: {
        usersApplied: {
          increment: 1,
        },
      },
    });

    return {
      status: true,
      message: 'Promo code applied successfully.',
    };
  }

  async updateUserLocation(
    userId: string,
    payload: Location,
  ): Promise<BaseResponseDTO> {
    await this.model.findByIdAndUpdate(userId, {
      lastLocation: {
        name: payload.name,
        location: {
          coordinates: [payload.lng, payload.lat],
        },
      },
      locationLastUpdated: new Date(),
    });

    return {
      status: true,
      message: 'Location updated.',
    };
  }
}

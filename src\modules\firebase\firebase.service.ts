import { FirebaseApp } from 'firebase/app';
import { Inject, Injectable } from '@nestjs/common';
import {
  ref,
  uploadString,
  getDownloadURL,
  getStorage,
  deleteObject,
} from 'firebase/storage';
import { readFileSync } from 'fs';
import { FileSystemStoredFile } from 'nestjs-form-data';
import { STORAGE_DIRECTORY } from 'src/utils/utils.enum';

@Injectable()
export class FirebaseService {
  constructor(@Inject('FIREBASE_APP') private firebaseApp: FirebaseApp) {}

  async uploadFile(
    userId: string,
    file: FileSystemStoredFile,
    dir: STORAGE_DIRECTORY,
    desc?: string,
  ) {
    const imgRef = ref(
      getStorage(this.firebaseApp),
      `${this.getUploadDirectory(userId, dir)}/${desc ? desc + '_' + file.originalName : file.originalName}`,
    );

    const base64String = readFileSync(file.path, 'base64');
    await uploadString(imgRef, base64String, 'base64');
    const download_url = getDownloadURL(imgRef);
    return download_url;
  }

  async deleteFile(url?: string): Promise<boolean> {
    if (!url) {
      return false;
    }
    try {
      const desertRef = ref(
        getStorage(this.firebaseApp),
        this.getFolderFromUrl(url),
      );
      await deleteObject(desertRef);
      return true;
    } catch {
      return false;
    }
  }

  private getUploadDirectory(userId: string, dir: STORAGE_DIRECTORY): string {
    return `${process.env.NODE_ENV}/${userId}/${dir}`;
  }

  private getFolderFromUrl(url: string): string {
    return decodeURIComponent(url).split('/o/')?.[1]?.split('?alt')?.[0];
  }
}

export function omit<Data extends object, <PERSON> extends keyof Data>(
  data: Data,
  keys: Keys[],
): Omit<Data, Keys> {
  const result = { ...data };

  for (const key of keys) {
    delete result[key];
  }

  return result as Omit<Data, Keys>;
}

export function pick<Data extends object, <PERSON> extends keyof Data>(
  data: Data,
  keys: Keys[],
): Pick<Data, Keys> {
  const result = {} as Pick<Data, Keys>;

  for (const key of keys) {
    result[key] = data[key];
  }

  return result;
}

export function calculatePaginationData(
  count: number,
  pageSize: number,
  currentPage: number,
) {
  const totalPages = Math.ceil(count / pageSize);

  return {
    page: currentPage,
    pageSize,
    totalRecords: count,
    totalPages,
    hasNext: currentPage < totalPages,
    hasPrevious: currentPage > 1,
  };
}

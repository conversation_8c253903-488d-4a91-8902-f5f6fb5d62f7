import { Prop, raw, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument, Types } from 'mongoose';
import { ROLE, SIGNUP_MODE, VERIFICATION_STATUS } from 'src/utils/utils.enum';
import { Place, PlaceSchema } from './location.schema';
import { Type } from 'class-transformer';
import { Card } from './card.schema';

export type UserDocument = HydratedDocument<User>;

@Schema()
export class User {
  @Prop()
  firstName: string;

  @Prop()
  lastName: string;

  @Prop({
    required: true,
    unique: true,
  })
  email: string;

  @Prop()
  phoneNumber: string;

  @Prop()
  password: string;

  @Prop({
    default: new Date(),
  })
  createdAt: Date;

  @Prop()
  hasPersonalVehicle: boolean;

  @Prop({
    default: false,
  })
  isVerified: boolean;

  @Prop({
    default: false,
  })
  isBanned: boolean;

  @Prop({
    default: VERIFICATION_STATUS.not_started,
  })
  verificationStatus: string;

  @Prop({
    default: 0,
  })
  __v: number;

  @Prop({
    default: ROLE.user,
  })
  role: ROLE;

  @Prop()
  addresses: {
    lat: number;
    lng: number;
    name: string;
  }[];

  @Prop(
    raw({
      carDetails: {
        make: {
          type: String,
        },
        model: {
          type: String,
        },
        colour: {
          type: String,
        },
        yearOfManufacture: {
          type: String,
        },
        chassisNumber: {
          type: String,
        },
        passengerCapacity: {
          type: Number,
        },
        plateNumber: {
          type: String,
        },
      },
      driverLicense_front: {
        type: String,
      },
      driverLicense_back: {
        type: String,
      },
      vehicleLicense: {
        type: String,
      },
      motorInsurance: {
        type: String,
      },
      roadWorthiness: {
        type: String,
      },
      identity: {
        type: {
          type: String,
        },
        value: {
          type: String,
        },
      },
    }),
  )
  verification: Record<string, any>;

  @Prop({
    type: PlaceSchema,
  })
  @Type(() => Place)
  lastLocation: Place;

  @Prop()
  locationLastUpdated: Date;

  @Prop()
  lastSeen: Date;

  @Prop({
    type: Boolean,
    default: false,
  })
  isOnline: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  privateRideActive: boolean;

  @Prop({
    type: String,
  })
  stripeCustomerId: string;

  @Prop({
    type: [
      {
        type: Types.ObjectId,
        ref: 'Card',
      },
    ],
  })
  cards: Card[];

  @Prop({
    type: String,
    default: SIGNUP_MODE.basic,
  })
  signupMode: SIGNUP_MODE;

  @Prop({
    type: mongoose.Schema.Types.Mixed, //id, usageCount
  })
  promoCode: Record<string, any>;
}

export const UserSchema = SchemaFactory.createForClass(User);
UserSchema.index({ location: '2dsphere' });

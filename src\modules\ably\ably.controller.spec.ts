import { Test, TestingModule } from '@nestjs/testing';
import { AblyController } from './ably.controller';
import { AblyService } from './ably.service';

describe('AblyController', () => {
  let controller: AblyController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AblyController],
      providers: [AblyService],
    }).compile();

    controller = module.get<AblyController>(AblyController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});

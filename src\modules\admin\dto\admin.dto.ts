import {
  IsIn,
  <PERSON>Int,
  <PERSON>ISO8601,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON>ber,
  Is<PERSON>ptional,
  IsString,
  Matches,
  Max,
  Min,
} from 'class-validator';
import { PaginationRequestDTO } from 'src/utils/utils.dto';
import { ROLE, TRIP_MODE, TRIP_STATUS } from 'src/utils/utils.enum';

export class GetAccountsDTO extends PaginationRequestDTO {
  @IsString()
  @IsNotEmpty()
  @IsIn([ROLE.user, ROLE.rider])
  type: string;
}

export class UserIDDTO {
  @IsString()
  @IsNotEmpty()
  userId: string;
}

export class GetTripsDTO extends PaginationRequestDTO {
  @IsString()
  @IsOptional()
  @IsIn(Object.values(TRIP_STATUS))
  status: TRIP_STATUS;

  @IsString()
  @IsOptional()
  @IsIn(Object.values(TRIP_MODE))
  type: TRIP_MODE;

  @IsString()
  @IsOptional()
  @IsISO8601()
  from: string;

  @IsString()
  @IsOptional()
  @IsISO8601()
  to: string;

  @IsString()
  @IsOptional()
  @Matches(/^\d+-\d+$/, {
    message: 'priceRange must be in the format minPrice-maxPrice  e.g 900-1500',
  })
  priceRange: string;
}

export class CreatePromoCodeDTO {
  @IsString()
  @IsNotEmpty()
  code: string;

  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100)
  discount: number;

  @IsNotEmpty()
  @IsNumber()
  maxDiscountValue: number;

  @IsNotEmpty()
  @IsInt()
  usageCount: number;

  @IsNotEmpty()
  @IsISO8601()
  expiresAt: string;
}

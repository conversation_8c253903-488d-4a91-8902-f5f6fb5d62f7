import { Modu<PERSON> } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'src/schemas/user.schema';
import { Trip, TripSchema } from 'src/schemas/trip.schema';

@Module({
  controllers: [AdminController],
  imports: [
    MongooseModule.forFeature([
      {
        name: User.name,
        schema: UserSchema,
      },
      {
        name: Trip.name,
        schema: TripSchema,
      },
    ]),
  ],
  providers: [AdminService],
})
export class AdminModule {}

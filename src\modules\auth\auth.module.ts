import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UserModule } from 'src/modules/user/user.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Token, TokenSchema } from 'src/schemas/token.schema';
import { StripeModule } from '../stripe/stripe.module';

@Module({
  controllers: [AuthController],
  providers: [AuthService],
  imports: [
    UserModule,
    MongooseModule.forFeature([{ name: Token.name, schema: TokenSchema }]),
    StripeModule,
  ],
})
export class AuthModule {}

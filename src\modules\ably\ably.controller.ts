import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Headers,
  Post,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { AblyService } from './ably.service';
import { AuthGuard } from 'src/utils/gaurds/auth.gaurd';
import { CurrentUser } from 'src/utils/decorators/custom.decorators';
import { AuthUser } from 'src/utils/utils.types';

@Controller('ably')
export class AblyController {
  constructor(private readonly ablyService: AblyService) {}

  @Post('webhook')
  async webHook(@Headers('coride-key') signature: string, @Body() body: any) {
    if (!signature) {
      throw new BadRequestException('Invalid request');
    }

    // Validate the signature using the body
    const isValid = process.env.ABLY_SERVER_KEY === signature;

    if (!isValid) {
      throw new UnauthorizedException('Invalid signature');
    }

    await this.ablyService.processPresenceEvent(body);

    return { message: 'Webhook received' };
  }

  @Get('token')
  @UseGuards(AuthGuard)
  async getAblyToken(@CurrentUser() user: AuthUser) {
    const tokenRequest = await this.ablyService.createTokenRequest(user.id);
    return tokenRequest;
  }
}
